#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
几何计算工具模块
包含旋转、切线计算、角度计算等几何相关功能
"""

import numpy as np


class GeometryUtils:
    """几何计算工具类"""

    @staticmethod
    def rotation_matrix(theta):
        """创建旋转矩阵"""
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        return np.array([[cos_theta, -sin_theta], [sin_theta, cos_theta]])

    @staticmethod
    def rotate_points_around_center(points, center, theta):
        """
        绕指定中心旋转点集

        Parameters:
        points: np.array - 点坐标数组
        center: np.array - 旋转中心
        theta: float - 旋转角度（弧度）

        Returns:
        rotated_points: np.array - 旋转后的点坐标
        """
        # 步骤1: 平移到旋转中心为原点
        centered_points = points - center

        # 步骤2: 应用旋转矩阵
        R = GeometryUtils.rotation_matrix(theta)
        rotated_centered = (R @ centered_points.T).T

        # 步骤3: 平移回原来的位置
        rotated_points = rotated_centered + center

        return rotated_points

    @staticmethod
    def calculate_vertex_angle(vertices, vertex_index):
        """计算顶点角度"""
        n = len(vertices)
        prev_vertex = vertices[(vertex_index - 1) % n]
        current_vertex = vertices[vertex_index]
        next_vertex = vertices[(vertex_index + 1) % n]

        # 计算两个边向量
        edge1 = prev_vertex - current_vertex
        edge2 = next_vertex - current_vertex

        # 计算角度
        cos_angle = np.dot(edge1, edge2) / (
            np.linalg.norm(edge1) * np.linalg.norm(edge2)
        )
        cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
        angle = np.arccos(cos_angle)

        return np.degrees(angle)

    @staticmethod
    def calc_tangent_points(cx, cy, radius, outside_point):
        """
        计算从外部点到圆的切点

        Parameters:
        cx, cy: float - 圆心坐标
        radius: float - 圆的半径
        outside_point: np.array - 外部点坐标 [x, y]

        Returns:
        tangent_points: list of np.array - 切点坐标列表
        """
        # 将实际的点做一次转换，因为下面的计算都是按圆心在原点计算的
        outside_x = outside_point[0] - cx
        outside_y = outside_point[1] - cy

        # 计算m值
        m = (outside_x**2 + outside_y**2) / (radius**2)

        if m <= 1:
            # 点在圆内或圆上，无切点
            return []

        # 计算sqrt(m-1)
        sqrt_m_minus_1 = np.sqrt(m - 1)

        # 求出的结果将会有4种排列
        point_a = np.array(
            [
                (outside_x + outside_y * sqrt_m_minus_1) / m,
                (outside_y + outside_x * sqrt_m_minus_1) / m,
            ]
        )

        point_b = np.array(
            [
                (outside_x - outside_y * sqrt_m_minus_1) / m,
                (outside_y - outside_x * sqrt_m_minus_1) / m,
            ]
        )

        point_c = np.array(
            [
                (outside_x + outside_y * sqrt_m_minus_1) / m,
                (outside_y - outside_x * sqrt_m_minus_1) / m,
            ]
        )

        point_d = np.array(
            [
                (outside_x - outside_y * sqrt_m_minus_1) / m,
                (outside_y + outside_x * sqrt_m_minus_1) / m,
            ]
        )

        # 实际上只会有2个切点，利用向量垂直，点乘结果是0来判断哪个是有效的
        valid_points = []
        tolerance = 1e-10

        for point in [point_a, point_b, point_c, point_d]:
            # 检查切线条件：从外部点到切点的向量与从切点到圆心的向量垂直
            to_tangent = point - np.array([outside_x, outside_y])
            to_center = np.array([0, 0]) - point  # 圆心在原点

            dot_product = np.dot(to_tangent, to_center)

            if abs(dot_product) <= tolerance:
                # 将坐标转换回原来的坐标系
                actual_point = point + np.array([cx, cy])
                valid_points.append(actual_point)

        return valid_points

    @staticmethod
    def line_segments_intersect(p1, p2, p3, p4):
        """检查两条线段是否相交"""

        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])

        return (ccw(p1, p3, p4) != ccw(p2, p3, p4)) and (
            ccw(p1, p2, p3) != ccw(p1, p2, p4)
        )

    @staticmethod
    def get_intersection_point(p1, p2, p3, p4):
        """计算两条直线的交点"""
        x1, y1 = p1
        x2, y2 = p2
        x3, y3 = p3
        x4, y4 = p4

        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return None

        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom

        intersection = np.array([x1 + t * (x2 - x1), y1 + t * (y2 - y1)])
        return intersection

    @staticmethod
    def calculate_perimeter(vertices):
        """计算多边形周长"""
        perimeter = 0
        n = len(vertices)
        for i in range(n):
            v1 = vertices[i]
            v2 = vertices[(i + 1) % n]
            perimeter += np.linalg.norm(v2 - v1)
        return perimeter

    @staticmethod
    def get_edge_length(vertices, start_idx, end_idx):
        """计算两个顶点之间的边长"""
        if start_idx == end_idx:
            return 0.0

        start_point = vertices[start_idx]
        end_point = vertices[end_idx]
        return np.linalg.norm(end_point - start_point)
