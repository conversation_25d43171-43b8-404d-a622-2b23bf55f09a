from fastapi import APIRouter, BackgroundTasks, HTTPException
from fastapi.responses import StreamingResponse
from app.schemas.simulation import SimulationCreate, Simulation, SimulationInfo
from app.models.simulation import (
    Simulation as DBSimulation,
    SimulationResult as DBResult,
)
from app.db.session import SessionLocal
from app.simulation import HexagonSimulation
import numpy as np
import pandas as pd
import io

router = APIRouter()


def run_simulation_task(simulation_id: int, params: dict):
    """
    The background task that runs the simulation and saves the results.
    """
    db = SessionLocal()
    try:
        # Update status to running
        db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
        if not db_sim:
            return
        db_sim.status = "running"
        db.commit()

        # Run the simulation
        sim = HexagonSimulation(**params)
        sim.run_simulation()
        results = sim.get_simulation_data()
        results["roller_A"] = sim.A.tolist()
        results["roller_B"] = sim.B.tolist()

        # Convert numpy arrays to lists for JSON serialization
        for key, value in results.items():
            if isinstance(value, np.ndarray):
                results[key] = value.tolist()

        # Create a summary
        summary = {
            "final_layer_number": int(results["layer_numbers"][-1]),
            "max_accumulated_thickness_mm": float(results["accumulated_thickness"][-1]),
            "total_film_consumption_mm": float(results["S_total"][-1]),
        }

        # Create a new result entry
        db_result = DBResult(
            simulation_id=simulation_id,
            results_data=results,
            summary_data=summary,
        )
        db.add(db_result)

        db_sim.status = "completed"
        db.commit()

    except Exception as e:
        db_sim.status = "failed"
        db.commit()
        print(f"Simulation {simulation_id} failed: {e}")
    finally:
        db.close()


@router.post("/", response_model=Simulation, status_code=201)
def create_simulation(
    simulation: SimulationCreate,
    background_tasks: BackgroundTasks,
):
    """
    Create a new simulation and start it in the background.
    """
    db = SessionLocal()
    db_sim = DBSimulation(status="pending", input_parameters=simulation.dict())
    db.add(db_sim)
    db.commit()
    db.refresh(db_sim)

    background_tasks.add_task(run_simulation_task, db_sim.id, simulation.dict())

    db.close()
    return db_sim


@router.get("/", response_model=list[SimulationInfo])
def read_simulations(skip: int = 0, limit: int = 100):
    """
    Retrieve a list of simulations with essential information.
    """
    db = SessionLocal()
    simulations = (
        db.query(DBSimulation)
        .order_by(DBSimulation.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )
    db.close()
    return simulations


@router.get("/{simulation_id}", response_model=Simulation)
def read_simulation(simulation_id: int):
    """
    Get a specific simulation by ID.
    """
    db = SessionLocal()
    db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
    db.close()
    if db_sim is None:
        raise HTTPException(status_code=404, detail="Simulation not found")
    return db_sim


@router.get("/{simulation_id}/status")
def get_simulation_status(simulation_id: int):
    """
    Get the status of a specific simulation.
    """
    db = SessionLocal()
    db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
    db.close()
    if db_sim is None:
        raise HTTPException(status_code=404, detail="Simulation not found")
    return {"status": db_sim.status}


@router.get("/{simulation_id}/results")
def get_simulation_results(simulation_id: int):
    """
    Get the results of a specific simulation.
    """
    db = SessionLocal()
    db_result = (
        db.query(DBResult).filter(DBResult.simulation_id == simulation_id).first()
    )
    db.close()
    if db_result is None:
        raise HTTPException(status_code=404, detail="Results not found")
    return db_result.results_data


@router.get("/{simulation_id}/export")
def export_simulation_results(simulation_id: int):
    """
    Export simulation results to an Excel file.
    """
    db = SessionLocal()
    db_result = (
        db.query(DBResult).filter(DBResult.simulation_id == simulation_id).first()
    )
    db.close()
    if db_result is None:
        raise HTTPException(status_code=404, detail="Results not found")

    results_data = db_result.results_data
    # Pad arrays to the same length
    max_len = 0
    for key, value in results_data.items():
        if isinstance(value, list):
            if len(value) > max_len:
                max_len = len(value)

    for key, value in results_data.items():
        if isinstance(value, list):
            len_diff = max_len - len(value)
            if len_diff > 0:
                results_data[key] = value + [None] * len_diff

    df = pd.DataFrame(results_data)

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="Simulation Data", index=False)

    output.seek(0)

    headers = {
        "Content-Disposition": f'attachment; filename="simulation_{simulation_id}_results.xlsx"'
    }
    return StreamingResponse(
        output,
        headers=headers,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )


@router.delete("/{simulation_id}", status_code=204)
def delete_simulation(simulation_id: int):
    """
    Delete a simulation and its results.
    """
    db = SessionLocal()
    db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
    if db_sim is None:
        db.close()
        raise HTTPException(status_code=404, detail="Simulation not found")

    # Delete associated results
    db.query(DBResult).filter(DBResult.simulation_id == simulation_id).delete()

    # Delete the simulation
    db.delete(db_sim)
    db.commit()
    db.close()
    return
