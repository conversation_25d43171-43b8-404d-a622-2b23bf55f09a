<template>
  <div class="history-view">
    <div class="history-header">
      <h3 class="history-title">
        <el-icon><Clock /></el-icon>
        仿真历史
      </h3>
      <el-button
        @click="refreshHistory"
        :loading="loading"
        size="small"
        class="refresh-btn"
      >
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <el-table
      :data="simulations"
      class="history-table"
      @row-click="selectSimulation"
      highlight-current-row
      :current-row-key="selectedRowId"
      row-key="id"
      empty-text="暂无仿真记录"
      v-loading="loading"
      element-loading-text="加载中..."
    >
      <el-table-column prop="id" label="ID" width="50" align="center">
        <template #default="scope">
          <span class="simulation-id">#{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="70" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="small">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="110">
        <template #default="scope">
          <span class="create-time">{{ formatDate(scope.row.created_at) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="60" align="center" fixed="right">
        <template #default="scope">
          <el-popconfirm
            title="确定要删除这个仿真记录吗？"
            confirm-button-text="删除"
            cancel-button-text="取消"
            @confirm="deleteSimulation(scope.row.id)"
          >
            <template #reference>
              <el-button
                @click.stop
                type="danger"
                size="small"
                :icon="Delete"
                class="delete-btn"
              >
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { Clock, Refresh, Delete } from '@element-plus/icons-vue';
import apiClient from '../api/client';
import { ElMessageBox, ElNotification } from 'element-plus';

export default {
  name: 'HistoryView',
  components: {
    Clock,
    Refresh,
    Delete,
  },
  emits: ['select-simulation'],
  setup(props, { emit }) {
    const simulations = ref([]);
    const loading = ref(false);
    const selectedRowId = ref(null);

    const fetchSimulations = async () => {
      loading.value = true;
      try {
        const response = await apiClient.getSimulations();
        simulations.value = response.data;
      } catch (error) {
        console.error('Failed to fetch simulations:', error);
        ElNotification({
          title: '加载失败',
          message: '无法加载仿真历史记录',
          type: 'error',
        });
      } finally {
        loading.value = false;
      }
    };

    const refreshHistory = () => {
      fetchSimulations();
    };

    const selectSimulation = (row) => {
      selectedRowId.value = row.id;
      emit('select-simulation', row.id);
    };

    const getStatusType = (status) => {
      const statusMap = {
        'running': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'pending': 'info'
      };
      return statusMap[status] || 'info';
    };

    const getStatusText = (status) => {
      const statusMap = {
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'pending': '等待中'
      };
      return statusMap[status] || status;
    };

    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const deleteSimulation = async (id) => {
      try {
        await apiClient.delete(`/simulations/${id}`);
        await fetchSimulations();
        ElNotification({
          title: '删除成功',
          message: '仿真记录已删除',
          type: 'success',
        });
      } catch (error) {
        console.error('Failed to delete simulation:', error);
        ElNotification({
          title: '删除失败',
          message: '无法删除仿真记录',
          type: 'error',
        });
      }
    };

    onMounted(fetchSimulations);

    return {
      simulations,
      loading,
      selectedRowId,
      selectSimulation,
      deleteSimulation,
      refreshHistory,
      getStatusType,
      getStatusText,
      formatDate,
      Delete,
    };
  },
};
</script>

<style scoped>
.history-view {
  margin-top: 25px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(116, 185, 255, 0.2);
  backdrop-filter: blur(10px);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(116, 185, 255, 0.3);
}

.history-title {
  color: #74b9ff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  text-shadow: 0 0 8px rgba(116, 185, 255, 0.4);
}

.history-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.refresh-btn {
  background: rgba(116, 185, 255, 0.1) !important;
  border: 1px solid rgba(116, 185, 255, 0.3) !important;
  color: #74b9ff !important;
  font-size: 12px !important;
}

.refresh-btn:hover {
  background: rgba(116, 185, 255, 0.2) !important;
  border-color: rgba(116, 185, 255, 0.6) !important;
}

.history-table {
  width: 100%;
}

.history-table :deep(.el-table) {
  background: transparent !important;
  color: #dfe6e9 !important;
}

.history-table :deep(.el-table__header) {
  background: rgba(45, 52, 54, 0.8) !important;
}

.history-table :deep(.el-table__header th) {
  background: rgba(45, 52, 54, 0.8) !important;
  color: #74b9ff !important;
  border-bottom: 2px solid rgba(116, 185, 255, 0.3) !important;
  font-weight: 600 !important;
  padding: 12px 8px !important;
}

.history-table :deep(.el-table__body tr) {
  background: rgba(255, 255, 255, 0.02) !important;
  transition: all 0.3s ease;
}

.history-table :deep(.el-table__body tr:hover) {
  background: rgba(116, 185, 255, 0.1) !important;
  cursor: pointer;
}

.history-table :deep(.el-table__body tr.current-row) {
  background: rgba(116, 185, 255, 0.15) !important;
  box-shadow: inset 0 0 0 2px rgba(116, 185, 255, 0.5);
}

.history-table :deep(.el-table__body td) {
  background: transparent !important;
  border-bottom: 1px solid rgba(116, 185, 255, 0.1) !important;
  color: #dfe6e9 !important;
  padding: 10px 8px !important;
}

.simulation-id {
  color: #00b894;
  font-weight: 600;
  font-size: 12px;
}

.create-time {
  color: #b2bec3;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.delete-btn {
  background: rgba(255, 99, 99, 0.1) !important;
  border: 1px solid rgba(255, 99, 99, 0.3) !important;
  color: #ff6b6b !important;
  padding: 4px 8px !important;
  min-width: 32px !important;
}

.delete-btn:hover {
  background: rgba(255, 99, 99, 0.2) !important;
  border-color: rgba(255, 99, 99, 0.6) !important;
  transform: scale(1.05);
}

.history-table :deep(.el-table__empty-text) {
  color: #636e72 !important;
  font-style: italic;
}

.history-table :deep(.el-tag) {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.history-table :deep(.el-tag--success) {
  background: rgba(0, 184, 148, 0.2);
  border-color: rgba(0, 184, 148, 0.4);
  color: #00b894;
}

.history-table :deep(.el-tag--warning) {
  background: rgba(253, 203, 110, 0.2);
  border-color: rgba(253, 203, 110, 0.4);
  color: #fdcb6e;
}

.history-table :deep(.el-tag--danger) {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.4);
  color: #ff6b6b;
}

.history-table :deep(.el-tag--info) {
  background: rgba(116, 185, 255, 0.2);
  border-color: rgba(116, 185, 255, 0.4);
  color: #74b9ff;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .history-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .history-table :deep(.el-table__header th),
  .history-table :deep(.el-table__body td) {
    padding: 8px 4px !important;
    font-size: 11px !important;
  }
}
</style>
