<template>
  <div class="history-view">
    <h3>Simulation History</h3>
    <el-table :data="simulations" style="width: 100%" @row-click="selectSimulation">
      <el-table-column prop="id" label="ID" width="60"></el-table-column>
      <el-table-column prop="created_at" label="Date" width="180">
        <template #default="scope">
          {{ new Date(scope.row.created_at).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="Status"></el-table-column>
      <el-table-column label="Actions">
        <template #default="scope">
          <el-button size="mini" type="danger" @click.stop="deleteSimulation(scope.row.id)">Delete</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import apiClient from '../api/client';
import { ElMessageBox, ElNotification } from 'element-plus';

export default {
  name: 'HistoryView',
  emits: ['select-simulation'],
  setup(props, { emit }) {
    const simulations = ref([]);

    const fetchSimulations = async () => {
      try {
        const response = await apiClient.getSimulations();
        simulations.value = response.data;
      } catch (error) {
        console.error('Failed to fetch simulations:', error);
      }
    };

    const selectSimulation = (row) => {
      emit('select-simulation', row.id);
    };

    const deleteSimulation = async (id) => {
      try {
        await ElMessageBox.confirm(
          'This will permanently delete the simulation. Continue?',
          'Warning',
          {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
          }
        );
        await apiClient.deleteSimulation(id);
        fetchSimulations(); // Refresh the list
        ElNotification({
          title: 'Success',
          message: 'Simulation deleted successfully.',
          type: 'success',
        });
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to delete simulation:', error);
          ElNotification({
            title: 'Error',
            message: 'Failed to delete simulation.',
            type: 'error',
          });
        }
      }
    };

    onMounted(fetchSimulations);

    return {
      simulations,
      selectSimulation,
      deleteSimulation,
    };
  },
};
</script>

<style scoped>
.history-view {
  margin-top: 20px;
}
</style>
