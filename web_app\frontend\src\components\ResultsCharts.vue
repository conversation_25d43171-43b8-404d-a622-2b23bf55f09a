<template>
  <div ref="chartContainer" style="width: 100%; height: 800px;"></div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

export default {
  name: 'ResultsCharts',
  props: {
    simulationData: {
      type: Object,
      default: () => null,
    },
  },
  setup(props) {
    const chartContainer = ref(null);
    let chart = null;

    const renderChart = () => {
      if (!props.simulationData || !chartContainer.value) {
        return;
      }
      if (chart) {
        chart.dispose();
      }
      chart = echarts.init(chartContainer.value);

      const { theta_deg, L, S, S_total, accumulated_thickness, layer_numbers, layer_consumption } = props.simulationData;

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['Tangent Length', 'Wrapped Length', 'Total Length', 'Thickness', 'Layer Number', 'Layer Consumption'],
          selected: {
            'Tangent Length': true,
            'Wrapped Length': true,
            'Total Length': true,
            'Thickness': false,
            'Layer Number': false,
            'Layer Consumption': false,
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: theta_deg,
          axisLine: { onZero: false },
          name: 'Rotation Angle (°)'
        },
        yAxis: [
          {
            name: 'Length (mm)',
            type: 'value',
          },
          {
            name: 'Thickness (mm)',
            type: 'value',
            position: 'right',
            offset: 80,
          },
          {
            name: 'Layers',
            type: 'value',
            position: 'right',
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 100,
          },
          {
            type: 'inside',
            start: 0,
            end: 100,
          }
        ],
        series: [
          { name: 'Tangent Length', type: 'line', yAxisIndex: 0, data: L, showSymbol: false },
          { name: 'Wrapped Length', type: 'line', yAxisIndex: 0, data: S, showSymbol: false },
          { name: 'Total Length', type: 'line', yAxisIndex: 0, data: S_total, showSymbol: false },
          { name: 'Thickness', type: 'line', yAxisIndex: 1, data: accumulated_thickness, showSymbol: false },
          { name: 'Layer Number', type: 'line', yAxisIndex: 2, data: layer_numbers, showSymbol: false },
          { name: 'Layer Consumption', type: 'line', yAxisIndex: 0, data: layer_consumption, showSymbol: false },
        ],
      };
      chart.setOption(option);
    };

    onMounted(renderChart);
    watch(() => props.simulationData, renderChart);

    return {
      chartContainer,
    };
  },
};
</script>
