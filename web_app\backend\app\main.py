from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.api import api_router
from app.core.config import API_V1_STR
from app.db.init_db import init_db
from app.db.session import SessionLocal

app = FastAPI(title="Hexagon Simulation API")


@app.on_event("startup")
def on_startup():
    db = SessionLocal()
    init_db(db)
    db.close()


# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=API_V1_STR)


@app.get("/")
def read_root():
    return {"message": "Welcome to the Hexagon Simulation API"}
