<template>
  <div>
    <el-button @click="downloadData" type="success" :disabled="!simulationData">Download Data</el-button>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="Charts" name="charts">
        <ResultsCharts :simulationData="simulationData" />
      </el-tab-pane>
      <el-tab-pane label="Animation" name="animation">
        <AnimationPlayer :simulationData="simulationData" ref="animationPlayer" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, nextTick } from 'vue';
import ResultsCharts from './ResultsCharts.vue';
import AnimationPlayer from './AnimationPlayer.vue';
import apiClient from '../api/client';

export default {
  name: 'ResultsDashboard',
  components: {
    ResultsCharts,
    AnimationPlayer,
  },
  props: {
    simulationData: {
      type: Object,
      default: () => null,
    },
    simulationId: {
      type: Number,
      default: null,
    }
  },
  setup(props, { expose }) {
    const activeTab = ref('charts');
    const animationPlayer = ref(null);

    const initializeAnimation = async () => {
      // Wait for next tick to ensure DOM is ready
      await nextTick();

      // Check if animationPlayer ref exists and has the correct method
      if (animationPlayer.value && animationPlayer.value.initializeAnimation) {
        try {
          await animationPlayer.value.initializeAnimation();
          console.log('Animation initialized successfully');
        } catch (error) {
          console.error('Error initializing animation:', error);
        }
      } else {
        console.error('AnimationPlayer ref not available or initializeAnimation method not found');
      }
    };

    const downloadData = async () => {
      if (!props.simulationId) return;
      try {
        const response = await apiClient.exportSimulationResults(props.simulationId);
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `simulation_${props.simulationId}_results.xlsx`);
        document.body.appendChild(link);
        link.click();
      } catch (error) {
        console.error('Failed to download results:', error);
      }
    };

    // Expose the initializeAnimation method to parent
    expose({
      initializeAnimation
    });

    return {
      activeTab,
      downloadData,
      animationPlayer,
      initializeAnimation,
    };
  },
};
</script>
