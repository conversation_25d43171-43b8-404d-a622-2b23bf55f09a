<template>
  <div class="container">
    <h1>六边形卷针薄膜包覆动态仿真</h1>
    <div class="subtitle" id="subtitle">
      旋转中心X偏移: {{ simulationData?.rotation_center_x_offset || 0.0 }} mm
    </div>

    <div id="animationContainer" ref="animationContainer">
      <canvas id="canvas" ref="canvas" width="1000" height="800"></canvas>
      <div class="info-panel" id="infoPanel" ref="infoPanel">
        <div v-html="infoPanelContent"></div>
      </div>
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color" style="background: blue; border: 1px dashed blue;"></div>
          <span>原始卷针</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: red;"></div>
          <span>当前卷针</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: green; height: 4px;"></div>
          <span>薄膜切线</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: red; width: 10px; height: 10px; border-radius: 50%;"></div>
          <span>接触点</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: green; width: 8px; height: 8px; border-radius: 50%;"></div>
          <span>过辊接触点</span>
        </div>
      </div>
    </div>

    <div class="controls">
      <el-button @click="togglePlay">{{ isPlaying ? '暂停' : '播放' }}</el-button>
      <div class="slider-container">
        <el-slider
          v-model="currentFrame"
          :max="maxFrames"
          @input="updateFrame"
          :disabled="isPlaying"
          style="flex: 1;"
        />
        <div class="frame-display">帧: {{ currentFrame }}/{{ maxFrames }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';

export default {
  name: 'AnimationPlayer',
  props: {
    simulationData: {
      type: Object,
      default: () => null,
    },
  },
  setup(props, { expose }) {
    // Refs
    const canvas = ref(null);
    const animationContainer = ref(null);
    const infoPanel = ref(null);
    const infoPanelContent = ref('');
    const currentFrame = ref(0);
    const maxFrames = ref(0);
    const isPlaying = ref(false);

    // Variables
    let ctx = null;
    let playInterval = null;
    const frameSkip = 10;

    // Transform parameters
    const scale = 4;
    const offsetX = 500;
    const offsetY = 400;

    // Transform functions
    const transformX = (x) => x * scale + offsetX;
    const transformY = (y) => -y * scale + offsetY;

    // Drawing functions
    const drawCircle = (x, y, radius, fillColor, strokeColor, lineWidth = 2, filled = false) => {
      if (!ctx) return;
      ctx.beginPath();
      ctx.arc(transformX(x), transformY(y), radius * (filled ? 1 : scale), 0, 2 * Math.PI);
      if (filled) {
        ctx.fillStyle = fillColor;
        ctx.fill();
      }
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = lineWidth;
      ctx.stroke();
    };

    const drawLine = (points, color, lineWidth = 2, dashed = false) => {
      if (!ctx || points.length < 2) return;
      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = lineWidth;
      ctx.setLineDash(dashed ? [5, 5] : []);
      ctx.moveTo(transformX(points[0][0]), transformY(points[0][1]));
      for (let i = 1; i < points.length; i++) {
        ctx.lineTo(transformX(points[i][0]), transformY(points[i][1]));
      }
      ctx.stroke();
      ctx.setLineDash([]);
    };

    const drawPolygon = (vertices, color, lineWidth = 2, dashed = false) => {
      if (!ctx || vertices.length < 3) return;
      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = lineWidth;
      ctx.setLineDash(dashed ? [5, 5] : []);
      ctx.moveTo(transformX(vertices[0][0]), transformY(vertices[0][1]));
      for (let i = 1; i < vertices.length; i++) {
        ctx.lineTo(transformX(vertices[i][0]), transformY(vertices[i][1]));
      }
      ctx.closePath();
      ctx.stroke();
      ctx.setLineDash([]);
    };

    const drawPoint = (x, y, color, size = 10) => {
      if (!ctx) return;
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(transformX(x), transformY(y), size / 2, 0, 2 * Math.PI);
      ctx.fill();
    };

    const drawText = (text, x, y, color = 'black', fontSize = 14) => {
      if (!ctx) return;
      ctx.fillStyle = color;
      ctx.font = `bold ${fontSize}px Arial`;
      ctx.fillText(text, transformX(x), transformY(y));
    };

    const drawWrappedArea = (vertices, contactPoint, wrappingLength) => {
      if (!ctx || !contactPoint || wrappingLength <= 0) return;

      // Draw wrapped film area with gradient
      const gradient = ctx.createRadialGradient(
        transformX(contactPoint[0]), transformY(contactPoint[1]), 0,
        transformX(contactPoint[0]), transformY(contactPoint[1]), wrappingLength * scale
      );
      gradient.addColorStop(0, 'rgba(0, 255, 100, 0.3)');
      gradient.addColorStop(1, 'rgba(0, 255, 100, 0.1)');

      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(transformX(contactPoint[0]), transformY(contactPoint[1]), wrappingLength * scale * 0.1, 0, 2 * Math.PI);
      ctx.fill();

      // Draw wrapping progress indicator
      ctx.strokeStyle = 'rgba(0, 255, 100, 0.8)';
      ctx.lineWidth = 3;
      ctx.setLineDash([5, 3]);
      ctx.beginPath();
      ctx.arc(transformX(contactPoint[0]), transformY(contactPoint[1]), wrappingLength * scale * 0.05, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.setLineDash([]);
    };

    const drawRoundedCorners = (vertices) => {
      if (!ctx || vertices.length < 3) return;

      // Draw small circles at vertices to indicate rounded corners
      vertices.forEach((vertex, index) => {
        const isSharpCorner = index === 0 || index === 3; // V1 and V4 are sharp corners
        const radius = isSharpCorner ? 2 : 4;
        const color = isSharpCorner ? 'rgba(255, 200, 0, 0.8)' : 'rgba(0, 200, 255, 0.8)';

        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(transformX(vertex[0]), transformY(vertex[1]), radius, 0, 2 * Math.PI);
        ctx.fill();
      });
    };

    const rotatePoint = (point, center, angle) => {
      const cos = Math.cos(angle);
      const sin = Math.sin(angle);
      const dx = point[0] - center[0];
      const dy = point[1] - center[1];
      return [
        center[0] + dx * cos - dy * sin,
        center[1] + dx * sin + dy * cos
      ];
    };

    const rotateVertices = (vertices, center, angle) => {
      return vertices.map(v => rotatePoint(v, center, angle));
    };

    const clearCanvas = () => {
      if (!ctx || !canvas.value) return;
      ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
      // Draw grid
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      for (let x = 0; x < canvas.value.width; x += 50) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.value.height);
        ctx.stroke();
      }
      for (let y = 0; y < canvas.value.height; y += 50) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.value.width, y);
        ctx.stroke();
      }
    };

    const drawStaticElements = () => {
      if (!props.simulationData || !ctx) return;

      const {
        A = props.simulationData?.roller_A || [0, 60],
        B = props.simulationData?.roller_B || [-30, 60],
        roller_radius = 20,
        geometric_center = [0, 0],
        rotation_center = [0, 0]
      } = props.simulationData;

      // Check if arrays are valid
      if (!Array.isArray(A) || A.length < 2 ||
          !Array.isArray(B) || B.length < 2 ||
          !Array.isArray(geometric_center) || geometric_center.length < 2 ||
          !Array.isArray(rotation_center) || rotation_center.length < 2) {
        console.warn('Invalid or missing position data in simulationData');
        return;
      }

      // Draw roller A
      drawCircle(A[0], A[1], roller_radius, 'blue', 'darkblue', 2);
      drawPoint(A[0], A[1], 'blue', 15);
      drawText('过辊A', A[0] + 3, A[1], 'blue', 14);

      // Draw roller B
      drawCircle(B[0], B[1], roller_radius, 'red', 'darkred', 2);
      drawPoint(B[0], B[1], 'red', 15);
      drawText('过辊B', B[0] - 10, B[1], 'red', 14);

      // Draw film path
      drawLine([[-30, 82], [0.5, 82]], 'black', 3);
      drawPoint(-30, 82, 'black', 8);
      drawPoint(0.5, 82, 'black', 8);

      // Draw centers
      drawPoint(geometric_center[0], geometric_center[1], 'green', 12);
      drawPoint(rotation_center[0], rotation_center[1], 'red', 12);
      drawLine([geometric_center, rotation_center], 'black', 2, true);
    };

    const drawFrame = (frameIndex) => {
      clearCanvas();
      drawStaticElements();

      if (!props.simulationData || !ctx) return;

      // Validate required data arrays
      const {
        original_vertices = [],
        rotation_center = [0, 0],
        theta = [],
        contact_points = [],
        roller_contact_points = [],
        upper_point_trajectory = [],
        lower_point_trajectory = [],
        A = props.simulationData?.roller_A || [0, 60],
        use_rounded_corners = false,
        accumulated_thickness = [],
        S = []
      } = props.simulationData;

      // Check if we have valid data for this frame
      if (frameIndex >= theta.length ||
          !Array.isArray(original_vertices) || original_vertices.length === 0 ||
          !Array.isArray(rotation_center) || rotation_center.length < 2) {
        console.warn('Invalid frame data or missing required arrays');
        return;
      }

      const i = frameIndex;

      // Validate vertices format (should be array of [x,y] pairs)
      const validVertices = original_vertices.every(v =>
        Array.isArray(v) && v.length >= 2 &&
        typeof v[0] === 'number' && typeof v[1] === 'number'
      );

      if (!validVertices) {
        console.warn('Invalid vertices format');
        return;
      }

      // Calculate current vertices with thickness effect
      let currentVertices = [...original_vertices];
      if (accumulated_thickness[i] && accumulated_thickness[i] > 0) {
        // Apply thickness effect to vertices (simplified)
        const thickness = accumulated_thickness[i];
        currentVertices = original_vertices.map(vertex => [
          vertex[0] * (1 + thickness * 0.001), // Scale slightly based on thickness
          vertex[1] * (1 + thickness * 0.001)
        ]);
      }

      // Draw original hexagon (blue dashed) - 原始卷针轮廓
      try {
        const originalRotated = rotateVertices(original_vertices, rotation_center, theta[i]);
        drawPolygon(originalRotated, 'rgba(0, 100, 255, 0.6)', 2, true);

        // Draw current hexagon with thickness (red solid) - 当前包覆状态
        const currentRotated = rotateVertices(currentVertices, rotation_center, theta[i]);
        drawPolygon(currentRotated, 'rgba(255, 50, 50, 0.8)', 3, false);

        // Draw wrapped area if there's wrapping length
        if (S[i] && S[i] > 0) {
          drawWrappedArea(currentRotated, contact_points[i], S[i]);
        }

        // Draw rounded corners if enabled
        if (use_rounded_corners) {
          drawRoundedCorners(currentRotated);
        }
      } catch (error) {
        console.error('Error drawing hexagon:', error);
      }

      // Draw contact point if available
      if (contact_points[i] && Array.isArray(contact_points[i]) && contact_points[i].length >= 2) {
        drawPoint(contact_points[i][0], contact_points[i][1], 'red', 10);
      }

      // Draw roller contact point if available
      if (roller_contact_points[i] && Array.isArray(roller_contact_points[i]) && roller_contact_points[i].length >= 2) {
        drawPoint(roller_contact_points[i][0], roller_contact_points[i][1], 'green', 8);

        // Draw film tangent line if both points exist
        if (contact_points[i] && Array.isArray(contact_points[i]) && contact_points[i].length >= 2) {
          drawLine([contact_points[i], roller_contact_points[i]], 'green', 3);
        }
      }

      // Draw special points if available
      if (upper_point_trajectory[i] && Array.isArray(upper_point_trajectory[i]) && upper_point_trajectory[i].length >= 2) {
        drawPoint(upper_point_trajectory[i][0], upper_point_trajectory[i][1], 'magenta', 10);

        // Draw connection line to A
        if (Array.isArray(A) && A.length >= 2) {
          drawLine([A, upper_point_trajectory[i]], 'magenta', 2, true);
        }
      }

      if (lower_point_trajectory[i] && Array.isArray(lower_point_trajectory[i]) && lower_point_trajectory[i].length >= 2) {
        drawPoint(lower_point_trajectory[i][0], lower_point_trajectory[i][1], 'cyan', 10);
      }

      // Draw trajectories
      if (i > 0) {
        // Contact points trajectory
        for (let j = 0; j < i && j < contact_points.length; j += 5) {
          if (contact_points[j] && Array.isArray(contact_points[j]) && contact_points[j].length >= 2) {
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.beginPath();
            ctx.arc(transformX(contact_points[j][0]), transformY(contact_points[j][1]), 1, 0, 2 * Math.PI);
            ctx.fill();
          }
        }

        // Upper point trajectory
        for (let j = 0; j < i && j < upper_point_trajectory.length; j += 5) {
          if (upper_point_trajectory[j] && Array.isArray(upper_point_trajectory[j]) && upper_point_trajectory[j].length >= 2) {
            ctx.fillStyle = 'rgba(255, 0, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(transformX(upper_point_trajectory[j][0]), transformY(upper_point_trajectory[j][1]), 1, 0, 2 * Math.PI);
            ctx.fill();
          }
        }

        // Lower point trajectory
        for (let j = 0; j < i && j < lower_point_trajectory.length; j += 5) {
          if (lower_point_trajectory[j] && Array.isArray(lower_point_trajectory[j]) && lower_point_trajectory[j].length >= 2) {
            ctx.fillStyle = 'rgba(0, 255, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(transformX(lower_point_trajectory[j][0]), transformY(lower_point_trajectory[j][1]), 1, 0, 2 * Math.PI);
            ctx.fill();
          }
        }
      }

      updateInfoPanel(i);
    };

    const updateInfoPanel = (frameIndex) => {
      if (!props.simulationData) return;

      const i = frameIndex;
      const {
        theta_deg = [],
        L = [],
        S = [],
        S_total = [],
        contact_type = [],
        upper_point_trajectory = [],
        lower_point_trajectory = [],
        roller_contact_distances = [],
        rotation_center_x_offset = 0,
        accumulated_thickness = [],
        layer_numbers = [],
        use_rounded_corners = false,
        film_thickness = 0.1
      } = props.simulationData;

      // Safely access array elements with fallback values
      const angle = theta_deg[i] || 0;
      const l_value = L[i] || 0;
      const s_value = S[i] || 0;
      const s_total_value = S_total[i] || 0;
      const contact = contact_type[i] || '-';
      const upper_point = upper_point_trajectory[i] || [0, 0];
      const lower_point = lower_point_trajectory[i] || [0, 0];
      const tangent_length = roller_contact_distances[i] || 0;
      const thickness = accumulated_thickness[i] || 0;
      const layers = layer_numbers[i] || 0;

      // Format contact type for better display
      const formatContactType = (type) => {
        if (type.includes('fixed_upper_point')) return '固定上方点';
        if (type.includes('leftmost_upper_point')) return '最左上方点';
        if (type.includes('corrected_V')) return `修正顶点${type.slice(-1)}`;
        if (type.includes('leftmost_V')) return `最左顶点${type.slice(-1)}`;
        if (type.includes('V')) return `顶点${type.slice(-1)}`;
        return type;
      };

      infoPanelContent.value = `
        <div class="info-title">🎯 仿真状态</div>
        <div class="info-row"><span class="label">旋转角度:</span> ${angle.toFixed(1)}°</div>
        <div class="info-row"><span class="label">当前层数:</span> ${layers}</div>
        <div class="info-row"><span class="label">累积厚度:</span> ${thickness.toFixed(3)} mm</div>

        <div class="info-title">📏 长度信息</div>
        <div class="info-row"><span class="label">总薄膜长度:</span> ${s_total_value.toFixed(2)} mm</div>
        <div class="info-row"><span class="label">　切线部分:</span> ${tangent_length.toFixed(2)} mm</div>
        <div class="info-row"><span class="label">　包覆部分:</span> ${s_value.toFixed(2)} mm</div>
        <div class="info-row"><span class="label">过辊距离:</span> ${l_value.toFixed(2)} mm</div>

        <div class="info-title">📍 接触信息</div>
        <div class="info-row"><span class="label">接触类型:</span> ${formatContactType(contact)}</div>
        <div class="info-row"><span class="label">上方点:</span> (${upper_point[0].toFixed(1)}, ${upper_point[1].toFixed(1)})</div>

        <div class="info-title">⚙️ 配置参数</div>
        <div class="info-row"><span class="label">X偏移:</span> ${rotation_center_x_offset} mm</div>
        <div class="info-row"><span class="label">膜厚:</span> ${film_thickness} mm</div>
        <div class="info-row"><span class="label">圆角模式:</span> ${use_rounded_corners ? '启用' : '禁用'}</div>
      `;
    };

    const updateFrame = (value) => {
      currentFrame.value = parseInt(value);
      if (props.simulationData && ctx) {
        drawFrame(currentFrame.value * frameSkip);
      }
    };

    const togglePlay = () => {
      isPlaying.value = !isPlaying.value;
      if (isPlaying.value) {
        if (currentFrame.value >= maxFrames.value) {
          currentFrame.value = 0;
        }
        playInterval = setInterval(() => {
          if (currentFrame.value < maxFrames.value) {
            currentFrame.value++;
            updateFrame(currentFrame.value);
          } else {
            togglePlay();
          }
        }, 50);
      } else {
        if (playInterval) {
          clearInterval(playInterval);
          playInterval = null;
        }
      }
    };

    const initializeAnimation = async () => {
      // Stop any existing animation
      if (isPlaying.value) {
        togglePlay();
      }

      // Clear any existing interval
      if (playInterval) {
        clearInterval(playInterval);
        playInterval = null;
      }

      // Wait for DOM to be ready
      await nextTick();

      if (!canvas.value) {
        console.error('Canvas element not found');
        return;
      }

      if (!props.simulationData) {
        console.warn('No simulation data available');
        return;
      }

      // Initialize canvas context
      ctx = canvas.value.getContext('2d');
      if (!ctx) {
        console.error('Failed to get canvas context');
        return;
      }

      // Validate simulation data structure
      const requiredFields = ['theta', 'original_vertices'];
      const missingFields = requiredFields.filter(field =>
        !props.simulationData[field] ||
        (Array.isArray(props.simulationData[field]) && props.simulationData[field].length === 0)
      );

      if (missingFields.length > 0) {
        console.warn('Missing or empty required fields in simulation data:', missingFields);
        // Still try to draw static elements even if animation data is missing
        clearCanvas();
        drawStaticElements();
        maxFrames.value = 0;
        currentFrame.value = 0;
        return;
      }

      // Calculate max frames
      if (props.simulationData.theta && props.simulationData.theta.length > 0) {
        maxFrames.value = Math.floor(props.simulationData.theta.length / frameSkip) - 1;
        currentFrame.value = 0;

        // Draw first frame
        try {
          drawFrame(0);
        } catch (error) {
          console.error('Error drawing first frame:', error);
          // At least draw static elements
          clearCanvas();
          drawStaticElements();
        }
      } else {
        console.warn('Simulation data theta array is empty or missing');
        maxFrames.value = 0;
        currentFrame.value = 0;
        // Draw static elements only
        clearCanvas();
        drawStaticElements();
      }
    };

    // Watch for simulation data changes
    watch(
      () => props.simulationData,
      async (newData) => {
        if (newData) {
          // Debug: Log the structure of received data
          console.log('Received simulation data:', {
            hasTheta: !!newData.theta,
            thetaLength: newData.theta?.length || 0,
            hasOriginalVertices: !!newData.original_vertices,
            verticesLength: newData.original_vertices?.length || 0,
            hasA: !!newData.A,
            hasB: !!newData.B,
            keys: Object.keys(newData)
          });

          await nextTick();
          await initializeAnimation();
        }
      },
      { deep: true, immediate: true }
    );

    // Lifecycle
    onMounted(async () => {
      if (props.simulationData && props.simulationData.theta) {
        await initializeAnimation();
      }
    });

    onUnmounted(() => {
      if (playInterval) {
        clearInterval(playInterval);
      }
    });

    // Expose methods to parent
    expose({
      initializeAnimation
    });

    return {
      canvas,
      animationContainer,
      infoPanel,
      infoPanelContent,
      currentFrame,
      maxFrames,
      isPlaying,
      togglePlay,
      updateFrame,
    };
  },
};
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

#animationContainer {
  position: relative;
  margin-bottom: 20px;
}

canvas {
  border: 1px solid #ddd;
  display: block;
  margin: 0 auto;
  background: #f0f0f0;
  border-radius: 4px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
}

.slider-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.frame-display {
  min-width: 100px;
  text-align: center;
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.info-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  background: linear-gradient(135deg, rgba(45, 52, 54, 0.95) 0%, rgba(99, 110, 114, 0.95) 100%);
  padding: 15px;
  border-radius: 8px;
  font-size: 12px;
  max-width: 280px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  line-height: 1.4;
  border: 1px solid rgba(116, 185, 255, 0.3);
  backdrop-filter: blur(10px);
}

.info-panel :deep(.info-title) {
  color: #74b9ff;
  font-weight: 600;
  font-size: 13px;
  margin: 8px 0 6px 0;
  text-shadow: 0 0 6px rgba(116, 185, 255, 0.5);
  border-bottom: 1px solid rgba(116, 185, 255, 0.2);
  padding-bottom: 3px;
}

.info-panel :deep(.info-title:first-child) {
  margin-top: 0;
}

.info-panel :deep(.info-row) {
  color: #dfe6e9;
  margin: 4px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-panel :deep(.label) {
  color: #b2bec3;
  font-weight: 500;
  min-width: 80px;
}

.legend {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-color {
  width: 20px;
  height: 3px;
  margin-right: 10px;
  border-radius: 2px;
}
</style>
