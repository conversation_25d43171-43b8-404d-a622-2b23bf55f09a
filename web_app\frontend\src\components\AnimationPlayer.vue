<template>
  <div class="container">
    <h1>六边形卷针薄膜包覆动态仿真</h1>
    <div class="subtitle" id="subtitle">
      旋转中心X偏移: {{ simulationData?.rotation_center_x_offset || 0.0 }} mm
    </div>

    <div id="animationContainer" ref="animationContainer">
      <canvas id="canvas" ref="canvas" width="1000" height="800"></canvas>
      <div class="info-panel" id="infoPanel" ref="infoPanel">
        <div v-html="infoPanelContent"></div>
      </div>
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color" style="background: blue; border: 1px dashed blue;"></div>
          <span>原始卷针</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: red;"></div>
          <span>当前卷针</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: green; height: 4px;"></div>
          <span>薄膜切线</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: red; width: 10px; height: 10px; border-radius: 50%;"></div>
          <span>接触点</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: green; width: 8px; height: 8px; border-radius: 50%;"></div>
          <span>过辊接触点</span>
        </div>
      </div>
    </div>

    <div class="controls">
      <el-button @click="togglePlay">{{ isPlaying ? '暂停' : '播放' }}</el-button>
      <div class="slider-container">
        <el-slider
          v-model="currentFrame"
          :max="maxFrames"
          @input="updateFrame"
          :disabled="isPlaying"
          style="flex: 1;"
        />
        <div class="frame-display">帧: {{ currentFrame }}/{{ maxFrames }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';

export default {
  name: 'AnimationPlayer',
  props: {
    simulationData: {
      type: Object,
      default: () => null,
    },
  },
  setup(props, { expose }) {
    // Refs
    const canvas = ref(null);
    const animationContainer = ref(null);
    const infoPanel = ref(null);
    const infoPanelContent = ref('');
    const currentFrame = ref(0);
    const maxFrames = ref(0);
    const isPlaying = ref(false);

    // Variables
    let ctx = null;
    let playInterval = null;
    const frameSkip = 10;

    // Transform parameters
    const scale = 4;
    const offsetX = 500;
    const offsetY = 400;

    // Transform functions
    const transformX = (x) => x * scale + offsetX;
    const transformY = (y) => -y * scale + offsetY;

    // Drawing functions
    const drawCircle = (x, y, radius, fillColor, strokeColor, lineWidth = 2, filled = false) => {
      if (!ctx) return;
      ctx.beginPath();
      ctx.arc(transformX(x), transformY(y), radius * (filled ? 1 : scale), 0, 2 * Math.PI);
      if (filled) {
        ctx.fillStyle = fillColor;
        ctx.fill();
      }
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = lineWidth;
      ctx.stroke();
    };

    const drawLine = (points, color, lineWidth = 2, dashed = false) => {
      if (!ctx || points.length < 2) return;
      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = lineWidth;
      ctx.setLineDash(dashed ? [5, 5] : []);
      ctx.moveTo(transformX(points[0][0]), transformY(points[0][1]));
      for (let i = 1; i < points.length; i++) {
        ctx.lineTo(transformX(points[i][0]), transformY(points[i][1]));
      }
      ctx.stroke();
      ctx.setLineDash([]);
    };

    const drawPolygon = (vertices, color, lineWidth = 2, dashed = false) => {
      if (!ctx || vertices.length < 3) return;
      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = lineWidth;
      ctx.setLineDash(dashed ? [5, 5] : []);
      ctx.moveTo(transformX(vertices[0][0]), transformY(vertices[0][1]));
      for (let i = 1; i < vertices.length; i++) {
        ctx.lineTo(transformX(vertices[i][0]), transformY(vertices[i][1]));
      }
      ctx.closePath();
      ctx.stroke();
      ctx.setLineDash([]);
    };

    const drawPoint = (x, y, color, size = 10) => {
      if (!ctx) return;
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(transformX(x), transformY(y), size / 2, 0, 2 * Math.PI);
      ctx.fill();
    };

    const drawText = (text, x, y, color = 'black', fontSize = 14) => {
      if (!ctx) return;
      ctx.fillStyle = color;
      ctx.font = `bold ${fontSize}px Arial`;
      ctx.fillText(text, transformX(x), transformY(y));
    };

    const rotatePoint = (point, center, angle) => {
      const cos = Math.cos(angle);
      const sin = Math.sin(angle);
      const dx = point[0] - center[0];
      const dy = point[1] - center[1];
      return [
        center[0] + dx * cos - dy * sin,
        center[1] + dx * sin + dy * cos
      ];
    };

    const rotateVertices = (vertices, center, angle) => {
      return vertices.map(v => rotatePoint(v, center, angle));
    };

    const clearCanvas = () => {
      if (!ctx || !canvas.value) return;
      ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
      // Draw grid
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      for (let x = 0; x < canvas.value.width; x += 50) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.value.height);
        ctx.stroke();
      }
      for (let y = 0; y < canvas.value.height; y += 50) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.value.width, y);
        ctx.stroke();
      }
    };

    const drawStaticElements = () => {
      if (!props.simulationData || !ctx) return;

      const {
        A = props.simulationData?.roller_A || [0, 60],
        B = props.simulationData?.roller_B || [-30, 60],
        roller_radius = 20,
        geometric_center = [0, 0],
        rotation_center = [0, 0]
      } = props.simulationData;

      // Check if arrays are valid
      if (!Array.isArray(A) || A.length < 2 ||
          !Array.isArray(B) || B.length < 2 ||
          !Array.isArray(geometric_center) || geometric_center.length < 2 ||
          !Array.isArray(rotation_center) || rotation_center.length < 2) {
        console.warn('Invalid or missing position data in simulationData');
        return;
      }

      // Draw roller A
      drawCircle(A[0], A[1], roller_radius, 'blue', 'darkblue', 2);
      drawPoint(A[0], A[1], 'blue', 15);
      drawText('过辊A', A[0] + 3, A[1], 'blue', 14);

      // Draw roller B
      drawCircle(B[0], B[1], roller_radius, 'red', 'darkred', 2);
      drawPoint(B[0], B[1], 'red', 15);
      drawText('过辊B', B[0] - 10, B[1], 'red', 14);

      // Draw film path
      drawLine([[-30, 82], [0.5, 82]], 'black', 3);
      drawPoint(-30, 82, 'black', 8);
      drawPoint(0.5, 82, 'black', 8);

      // Draw centers
      drawPoint(geometric_center[0], geometric_center[1], 'green', 12);
      drawPoint(rotation_center[0], rotation_center[1], 'red', 12);
      drawLine([geometric_center, rotation_center], 'black', 2, true);
    };

    const drawFrame = (frameIndex) => {
      clearCanvas();
      drawStaticElements();

      if (!props.simulationData || !ctx) return;

      // Validate required data arrays
      const {
        original_vertices = [],
        rotation_center = [0, 0],
        theta = [],
        contact_points = [],
        roller_contact_points = [],
        upper_point_trajectory = [],
        lower_point_trajectory = [],
        A = props.simulationData?.roller_A || [0, 60]
      } = props.simulationData;

      // Check if we have valid data for this frame
      if (frameIndex >= theta.length ||
          !Array.isArray(original_vertices) || original_vertices.length === 0 ||
          !Array.isArray(rotation_center) || rotation_center.length < 2) {
        console.warn('Invalid frame data or missing required arrays');
        return;
      }

      const i = frameIndex;

      // Validate vertices format (should be array of [x,y] pairs)
      const validVertices = original_vertices.every(v =>
        Array.isArray(v) && v.length >= 2 &&
        typeof v[0] === 'number' && typeof v[1] === 'number'
      );

      if (!validVertices) {
        console.warn('Invalid vertices format');
        return;
      }

      // Draw original hexagon (blue dashed)
      try {
        const originalRotated = rotateVertices(original_vertices, rotation_center, theta[i]);
        drawPolygon(originalRotated, 'rgba(0, 0, 255, 0.5)', 2, true);

        // Draw current hexagon (red solid)
        drawPolygon(originalRotated, 'red', 2, false);
      } catch (error) {
        console.error('Error drawing hexagon:', error);
      }

      // Draw contact point if available
      if (contact_points[i] && Array.isArray(contact_points[i]) && contact_points[i].length >= 2) {
        drawPoint(contact_points[i][0], contact_points[i][1], 'red', 10);
      }

      // Draw roller contact point if available
      if (roller_contact_points[i] && Array.isArray(roller_contact_points[i]) && roller_contact_points[i].length >= 2) {
        drawPoint(roller_contact_points[i][0], roller_contact_points[i][1], 'green', 8);

        // Draw film tangent line if both points exist
        if (contact_points[i] && Array.isArray(contact_points[i]) && contact_points[i].length >= 2) {
          drawLine([contact_points[i], roller_contact_points[i]], 'green', 3);
        }
      }

      // Draw special points if available
      if (upper_point_trajectory[i] && Array.isArray(upper_point_trajectory[i]) && upper_point_trajectory[i].length >= 2) {
        drawPoint(upper_point_trajectory[i][0], upper_point_trajectory[i][1], 'magenta', 10);

        // Draw connection line to A
        if (Array.isArray(A) && A.length >= 2) {
          drawLine([A, upper_point_trajectory[i]], 'magenta', 2, true);
        }
      }

      if (lower_point_trajectory[i] && Array.isArray(lower_point_trajectory[i]) && lower_point_trajectory[i].length >= 2) {
        drawPoint(lower_point_trajectory[i][0], lower_point_trajectory[i][1], 'cyan', 10);
      }

      // Draw trajectories
      if (i > 0) {
        // Contact points trajectory
        for (let j = 0; j < i && j < contact_points.length; j += 5) {
          if (contact_points[j] && Array.isArray(contact_points[j]) && contact_points[j].length >= 2) {
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.beginPath();
            ctx.arc(transformX(contact_points[j][0]), transformY(contact_points[j][1]), 1, 0, 2 * Math.PI);
            ctx.fill();
          }
        }

        // Upper point trajectory
        for (let j = 0; j < i && j < upper_point_trajectory.length; j += 5) {
          if (upper_point_trajectory[j] && Array.isArray(upper_point_trajectory[j]) && upper_point_trajectory[j].length >= 2) {
            ctx.fillStyle = 'rgba(255, 0, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(transformX(upper_point_trajectory[j][0]), transformY(upper_point_trajectory[j][1]), 1, 0, 2 * Math.PI);
            ctx.fill();
          }
        }

        // Lower point trajectory
        for (let j = 0; j < i && j < lower_point_trajectory.length; j += 5) {
          if (lower_point_trajectory[j] && Array.isArray(lower_point_trajectory[j]) && lower_point_trajectory[j].length >= 2) {
            ctx.fillStyle = 'rgba(0, 255, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(transformX(lower_point_trajectory[j][0]), transformY(lower_point_trajectory[j][1]), 1, 0, 2 * Math.PI);
            ctx.fill();
          }
        }
      }

      updateInfoPanel(i);
    };

    const updateInfoPanel = (frameIndex) => {
      if (!props.simulationData) return;

      const i = frameIndex;
      const {
        theta_deg = [],
        L = [],
        S = [],
        S_total = [],
        contact_type = [],
        upper_point_trajectory = [],
        lower_point_trajectory = [],
        roller_contact_distances = [],
        rotation_center_x_offset = 0
      } = props.simulationData;

      // Safely access array elements with fallback values
      const angle = theta_deg[i] || 0;
      const l_value = L[i] || 0;
      const s_value = S[i] || 0;
      const s_total_value = S_total[i] || 0;
      const contact = contact_type[i] || '-';
      const upper_point = upper_point_trajectory[i] || [0, 0];
      const lower_point = lower_point_trajectory[i] || [0, 0];
      const tangent_length = roller_contact_distances[i] || 0;

      infoPanelContent.value = `
        角度: ${angle.toFixed(1)}°<br>
        过辊到接触点: ${l_value.toFixed(2)}<br>
        总薄膜长度: ${s_total_value.toFixed(2)}<br>
        &nbsp;&nbsp;切线部分: ${tangent_length.toFixed(2)}<br>
        &nbsp;&nbsp;包覆部分: ${s_value.toFixed(2)}<br>
        接触类型: ${contact}<br>
        上方点: (${upper_point[0].toFixed(1)}, ${upper_point[1].toFixed(1)})<br>
        下方点: (${lower_point[0].toFixed(1)}, ${lower_point[1].toFixed(1)})<br>
        X偏移: ${rotation_center_x_offset} mm
      `;
    };

    const updateFrame = (value) => {
      currentFrame.value = parseInt(value);
      if (props.simulationData && ctx) {
        drawFrame(currentFrame.value * frameSkip);
      }
    };

    const togglePlay = () => {
      isPlaying.value = !isPlaying.value;
      if (isPlaying.value) {
        if (currentFrame.value >= maxFrames.value) {
          currentFrame.value = 0;
        }
        playInterval = setInterval(() => {
          if (currentFrame.value < maxFrames.value) {
            currentFrame.value++;
            updateFrame(currentFrame.value);
          } else {
            togglePlay();
          }
        }, 50);
      } else {
        if (playInterval) {
          clearInterval(playInterval);
          playInterval = null;
        }
      }
    };

    const initializeAnimation = async () => {
      // Stop any existing animation
      if (isPlaying.value) {
        togglePlay();
      }

      // Clear any existing interval
      if (playInterval) {
        clearInterval(playInterval);
        playInterval = null;
      }

      // Wait for DOM to be ready
      await nextTick();

      if (!canvas.value) {
        console.error('Canvas element not found');
        return;
      }

      if (!props.simulationData) {
        console.warn('No simulation data available');
        return;
      }

      // Initialize canvas context
      ctx = canvas.value.getContext('2d');
      if (!ctx) {
        console.error('Failed to get canvas context');
        return;
      }

      // Validate simulation data structure
      const requiredFields = ['theta', 'original_vertices'];
      const missingFields = requiredFields.filter(field =>
        !props.simulationData[field] ||
        (Array.isArray(props.simulationData[field]) && props.simulationData[field].length === 0)
      );

      if (missingFields.length > 0) {
        console.warn('Missing or empty required fields in simulation data:', missingFields);
        // Still try to draw static elements even if animation data is missing
        clearCanvas();
        drawStaticElements();
        maxFrames.value = 0;
        currentFrame.value = 0;
        return;
      }

      // Calculate max frames
      if (props.simulationData.theta && props.simulationData.theta.length > 0) {
        maxFrames.value = Math.floor(props.simulationData.theta.length / frameSkip) - 1;
        currentFrame.value = 0;

        // Draw first frame
        try {
          drawFrame(0);
        } catch (error) {
          console.error('Error drawing first frame:', error);
          // At least draw static elements
          clearCanvas();
          drawStaticElements();
        }
      } else {
        console.warn('Simulation data theta array is empty or missing');
        maxFrames.value = 0;
        currentFrame.value = 0;
        // Draw static elements only
        clearCanvas();
        drawStaticElements();
      }
    };

    // Watch for simulation data changes
    watch(
      () => props.simulationData,
      async (newData) => {
        if (newData) {
          // Debug: Log the structure of received data
          console.log('Received simulation data:', {
            hasTheta: !!newData.theta,
            thetaLength: newData.theta?.length || 0,
            hasOriginalVertices: !!newData.original_vertices,
            verticesLength: newData.original_vertices?.length || 0,
            hasA: !!newData.A,
            hasB: !!newData.B,
            keys: Object.keys(newData)
          });

          await nextTick();
          await initializeAnimation();
        }
      },
      { deep: true, immediate: true }
    );

    // Lifecycle
    onMounted(async () => {
      if (props.simulationData && props.simulationData.theta) {
        await initializeAnimation();
      }
    });

    onUnmounted(() => {
      if (playInterval) {
        clearInterval(playInterval);
      }
    });

    // Expose methods to parent
    expose({
      initializeAnimation
    });

    return {
      canvas,
      animationContainer,
      infoPanel,
      infoPanelContent,
      currentFrame,
      maxFrames,
      isPlaying,
      togglePlay,
      updateFrame,
    };
  },
};
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

#animationContainer {
  position: relative;
  margin-bottom: 20px;
}

canvas {
  border: 1px solid #ddd;
  display: block;
  margin: 0 auto;
  background: #f0f0f0;
  border-radius: 4px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
}

.slider-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.frame-display {
  min-width: 100px;
  text-align: center;
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.info-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 200, 0.95);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  max-width: 250px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  line-height: 1.6;
}

.legend {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-color {
  width: 20px;
  height: 3px;
  margin-right: 10px;
  border-radius: 2px;
}
</style>
