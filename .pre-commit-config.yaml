default_language_version:
  python: python3

exclude: |
  (?x)^(
    alembic/|
    _grpc.py|
    _pb2.py|
    tests/conftest.py
  )

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-added-large-files
      - id: check-merge-conflict

  # 使用 ruff 替代 flake8，因为它包含了 flake8 的功能且速度更快
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.4
    hooks:
      - id: ruff
        args: [--fix]
      - id: ruff-format

  # 添加类型检查（原配置中没有）
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.17.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - types-pyyaml
          - types-setuptools
          - types-psycopg2
        exclude: ^tests/
