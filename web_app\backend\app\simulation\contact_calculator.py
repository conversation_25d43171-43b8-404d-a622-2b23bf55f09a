#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
接触点计算模块
包含接触点查找、包覆长度计算等功能
"""

import numpy as np
from .geometry_utils import GeometryUtils


class ContactCalculator:
    """接触点计算器类"""

    def __init__(self, roller_A, roller_radius, rounded_corner_calc=None):
        """
        初始化接触点计算器

        Parameters:
        roller_A: np.array - 过辊A的位置
        roller_radius: float - 过辊半径
        rounded_corner_calc: RoundedCornerCalculator - 圆角计算器（可选）
        """
        self.roller_A = roller_A
        self.roller_radius = roller_radius
        self.rounded_corner_calc = rounded_corner_calc
        self.last_contact_info = None  # 存储最后一次圆角接触信息

    def find_roller_contact_point(self, contact_point):
        """
        计算从接触点到过辊的切点
        选择在A点左侧的切点（x > 0.5的点）

        Parameters:
        contact_point: np.array - 当前的接触点坐标

        Returns:
        roller_contact_point: np.array - 过辊上的接触点坐标
        """
        # 计算从接触点到过辊圆心的切点
        tangent_points = GeometryUtils.calc_tangent_points(
            self.roller_A[0], self.roller_A[1], self.roller_radius, contact_point
        )

        if not tangent_points:
            # 如果没有切点，直接返回过辊圆心
            return self.roller_A

        # 选择在A点左侧的切点（x坐标大于0.5的点）
        valid_points = []
        for point in tangent_points:
            if point[0] > 0.5:  # x > 0.5
                valid_points.append(point)

        if not valid_points:
            # 如果没有满足条件的切点，选择最接近的
            return tangent_points[0]

        # 如果有多个满足条件的点，选择x坐标最大的（最左侧）
        leftmost_point = max(valid_points, key=lambda p: p[0])
        return leftmost_point

    def find_leftmost_valid_contact_point(
        self, rotated_vertices, rotated_upper, angle_deg
    ):
        """
        根据正确的包覆规则寻找接触点，并检查几何约束
        支持圆角仿真模式

        Parameters:
        rotated_vertices: np.array - 旋转后的顶点
        rotated_upper: np.array - 旋转后的上方点
        angle_deg: float - 当前角度（度）

        Returns:
        contact_point: np.array - 接触点坐标
        contact_type: str - 接触点类型
        """
        # 初始状态：固定连接到(2,4)点，保持到接近90度
        if angle_deg < 85:  # 扩大固定连接的角度范围
            return rotated_upper, "fixed_upper_point"

        # 如果启用了圆角仿真，使用圆角逻辑
        if self.rounded_corner_calc is not None:
            contact_point, contact_info = self._find_contact_point_rounded_mode(
                rotated_vertices, rotated_upper, angle_deg
            )

            # 如果返回的是复杂信息（字典），提取接触点类型
            if isinstance(contact_info, dict):
                contact_type = contact_info["type"]
                # 将完整信息存储在接触点计算器中，供后续使用
                self.last_contact_info = contact_info
            else:
                contact_type = contact_info
                self.last_contact_info = None

            return contact_point, contact_type
        else:
            return self._find_contact_point_sharp_mode(
                rotated_vertices, rotated_upper, angle_deg
            )

    def _find_contact_point_sharp_mode(
        self, rotated_vertices, rotated_upper, angle_deg
    ):
        """尖角模式的接触点查找"""
        # 收集候选点
        candidate_points = []
        candidate_info = []

        if angle_deg < 180:
            # 旋转小于180度：考虑V3、V4、V5（索引2、3、4）+ (2,4)点
            valid_vertex_indices = [2, 3, 4]  # V3, V4, V5

            # 添加六边形顶点
            for idx in valid_vertex_indices:
                vertex = rotated_vertices[idx]
                candidate_points.append(vertex)
                candidate_info.append(
                    {"point": vertex, "type": f"vertex_V{idx + 1}", "x": vertex[0]}
                )

            # 添加旋转后的(2,4)点
            candidate_points.append(rotated_upper)
            candidate_info.append(
                {"point": rotated_upper, "type": "upper_point", "x": rotated_upper[0]}
            )
        else:
            # 旋转超过180度：考虑所有顶点
            for idx in range(len(rotated_vertices)):
                vertex = rotated_vertices[idx]
                candidate_points.append(vertex)
                candidate_info.append(
                    {"point": vertex, "type": f"vertex_V{idx + 1}", "x": vertex[0]}
                )

        # 在所有候选点中寻找最左侧的点（X坐标最小）
        if not candidate_info:
            return rotated_upper, "upper_point"

        leftmost_candidate = min(candidate_info, key=lambda x: x["x"])
        leftmost_point = leftmost_candidate["point"]
        leftmost_type = leftmost_candidate["type"]

        # 确定接触点类型名称
        if leftmost_type == "upper_point":
            contact_type = "leftmost_upper_point"
        elif leftmost_type.startswith("vertex_"):
            vertex_name = leftmost_type.replace("vertex_", "")
            contact_type = f"leftmost_{vertex_name}"
        else:
            contact_type = leftmost_type

        # 几何约束检查：检查切线是否会穿过多边形内部
        next_vertex, next_vertex_name = self._get_next_vertex_info(
            contact_type, rotated_vertices, rotated_upper
        )

        # 计算角度关系
        tangent_angle_deg, vertex_angle_deg, angle_diff = (
            self._calculate_angle_from_contact_point(
                leftmost_point, self.roller_A, next_vertex
            )
        )

        # 判断切线是否会穿过多边形内部
        if 0 < angle_diff < 180:
            # 检测到切线穿过内部，使用下一个顶点作为接触点
            corrected_contact = next_vertex
            corrected_type = f"corrected_{next_vertex_name}"
            return corrected_contact, corrected_type
        else:
            # 切线角度合理，使用原始接触点
            return leftmost_point, contact_type

    def _find_contact_point_rounded_mode(
        self, rotated_vertices, rotated_upper, angle_deg
    ):
        """
        圆角模式的接触点查找
        基于main_coordinate_tangent_analysis.py的逻辑
        """
        # 使用main.py的最左侧点逻辑确定选中的顶点
        vn_point, vn_contact_type = self._find_leftmost_valid_contact_point_main_logic(
            rotated_vertices, rotated_upper, angle_deg
        )

        # 确定选中的顶点索引和对应的内切圆
        selected_vertex_index = None

        if angle_deg < 85:
            # 0-85度：固定使用(2,4)点，不对应任何内切圆
            return rotated_upper, "fixed_upper_point"
        else:
            # 85度后：确定选中的是哪个顶点
            for i, vertex in enumerate(rotated_vertices):
                if np.allclose(vertex, vn_point, atol=1e-6):
                    selected_vertex_index = i
                    break

            if selected_vertex_index is not None:
                # 计算旋转角度和旋转后的圆心
                angle_rad = np.deg2rad(angle_deg)
                # 假设旋转中心为 (geometric_center[0] + 2.0, geometric_center[1])
                geometric_center = np.mean(
                    self.rounded_corner_calc.original_vertices, axis=0
                )
                rotation_center = np.array(
                    [geometric_center[0] + 2.0, geometric_center[1]]
                )

                # 获取旋转后的圆心
                rotated_centers = self.rounded_corner_calc.get_rotated_centers(
                    rotation_center, angle_rad
                )
                selected_circle_center = rotated_centers[selected_vertex_index]
                selected_circle_radius = (
                    self.rounded_corner_calc.sharp_radius
                    if selected_vertex_index in [0, 3]
                    else self.rounded_corner_calc.blunt_radius
                )

                # 计算选中内切圆与过辊A的两圆公切线
                cross_result = (
                    self.rounded_corner_calc.calculate_cross_tangent_with_constraint(
                        self.roller_A,
                        self.roller_radius,
                        selected_circle_center,
                        selected_circle_radius,
                    )
                )

                if cross_result and cross_result.get("valid_tangents"):
                    # 选择满足约束的切线
                    valid_tangents = cross_result["valid_tangents"]
                    if valid_tangents:
                        selected_tangent = valid_tangents[0]  # 选择第一个有效切线
                        actual_film_start = selected_tangent["diamond_point"]

                        # 存储额外信息用于长度计算
                        contact_info = {
                            "point": actual_film_start,
                            "type": f"rounded_V{selected_vertex_index + 1}",
                            "vertex_index": selected_vertex_index,
                            "circle_center": selected_circle_center,
                            "circle_radius": selected_circle_radius,
                            "tangent_info": selected_tangent,
                            "rotated_vertices": rotated_vertices,
                        }

                        return actual_film_start, contact_info
                    else:
                        # 备用方案：直接从顶点计算
                        return vn_point, f"vertex_V{selected_vertex_index + 1}"
                else:
                    # 备用方案：直接从顶点计算
                    return vn_point, f"vertex_V{selected_vertex_index + 1}"
            else:
                # 如果不是顶点，可能是修正后的点
                return vn_point, vn_contact_type

    def _find_leftmost_valid_contact_point_main_logic(
        self, rotated_vertices, rotated_upper, angle_deg
    ):
        """
        使用main.py的最左侧点逻辑
        这里简化实现，实际应该调用main.py的方法
        """
        # 简化版本：直接使用尖角模式的逻辑
        return self._find_contact_point_sharp_mode(
            rotated_vertices, rotated_upper, angle_deg
        )

    def get_rounded_contact_info(self):
        """
        获取最后一次圆角接触计算的详细信息

        Returns:
        dict or None: 包含圆弧长度、切线长度等信息的字典
        """
        return self.last_contact_info

    def calculate_rounded_wrapping_length(self):
        """
        计算圆角模式下的包覆长度

        Returns:
        tuple: (arc_length, tangent_length, total_length)
        """
        if self.last_contact_info is None:
            return 0.0, 0.0, 0.0

        info = self.last_contact_info

        # 计算圆弧长度：从圆弧起点到公切线切点
        arc_length = self.rounded_corner_calc.calculate_arc_length_to_tangent_point(
            info["circle_center"],
            info["circle_radius"],
            info["vertex_index"],
            info["rotated_vertices"],
            info["tangent_info"]["diamond_point"],
        )

        # 公切线长度
        tangent_length = info["tangent_info"]["length"]

        # 总包覆长度
        total_length = arc_length + tangent_length

        return arc_length, tangent_length, total_length

    def _get_next_vertex_info(self, contact_type, rotated_vertices, rotated_upper):
        """根据接触点类型确定下一个顶点（逆时针方向）"""
        vertex_sequence = [4, 3, 2, 1, 0, 5]  # V5->V4->V3->V2->V1->V6 的索引序列

        if "upper_point" in contact_type:
            next_vertex_idx = 4  # V5
            next_vertex = rotated_vertices[next_vertex_idx]
            next_vertex_name = "V5"
        else:
            # 如果是顶点，找到在序列中的下一个
            current_vertex_idx = None
            if "V5" in contact_type:
                current_vertex_idx = 4
            elif "V4" in contact_type:
                current_vertex_idx = 3
            elif "V3" in contact_type:
                current_vertex_idx = 2
            elif "V2" in contact_type:
                current_vertex_idx = 1
            elif "V1" in contact_type:
                current_vertex_idx = 0
            elif "V6" in contact_type:
                current_vertex_idx = 5

            if current_vertex_idx is not None:
                current_pos = vertex_sequence.index(current_vertex_idx)
                next_pos = (current_pos + 1) % len(vertex_sequence)
                next_vertex_idx = vertex_sequence[next_pos]
                next_vertex = rotated_vertices[next_vertex_idx]
                next_vertex_name = f"V{next_vertex_idx + 1}"
            else:
                # 如果无法确定，默认使用V5
                next_vertex_idx = 4
                next_vertex = rotated_vertices[next_vertex_idx]
                next_vertex_name = "V5"

        return next_vertex, next_vertex_name

    def _calculate_angle_from_contact_point(self, contact_point, roller_A, next_vertex):
        """以接触点为中心计算角度，用于检查切线是否穿过多边形内部"""
        # 从接触点指向过辊A的角度
        contact_to_roller = roller_A - contact_point
        tangent_angle = np.arctan2(contact_to_roller[1], contact_to_roller[0])
        tangent_angle_deg = np.rad2deg(tangent_angle)

        # 从接触点指向下一个顶点的角度
        contact_to_vertex = next_vertex - contact_point
        vertex_angle = np.arctan2(contact_to_vertex[1], contact_to_vertex[0])
        vertex_angle_deg = np.rad2deg(vertex_angle)

        # 计算角度差
        angle_diff = vertex_angle_deg - tangent_angle_deg

        # 处理角度跨越±180°边界的情况
        if angle_diff > 180:
            angle_diff -= 360
        elif angle_diff < -180:
            angle_diff += 360

        return tangent_angle_deg, vertex_angle_deg, angle_diff
