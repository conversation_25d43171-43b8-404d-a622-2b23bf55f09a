from pydantic import BaseModel
from typing import Dict, Any
from datetime import datetime


class SimulationBase(BaseModel):
    pin_shape: str = "hexagon"
    rotation_center_x_offset: float = 2.0
    film_thickness: float = 0.1
    total_rotation: int = 3600
    step_angle: float = 0.5
    use_rounded_corners: bool = False
    sharp_radius: float = 0.8
    blunt_radius: float = 12.0
    roller_A_x: float = 0.0
    roller_A_y: float = 60.0
    roller_B_x: float = -30.0
    roller_B_y: float = 60.0
    roller_radius: float = 2.0
    v1_x: float = -30.0
    v1_y: float = 0.0
    v2_x: float = -20.0
    v2_y: float = -4.0
    v3_x: float = 20.0
    v3_y: float = -4.0
    v4_x: float = 30.0
    v4_y: float = 0.0
    v5_x: float = 20.0
    v5_y: float = 4.0
    v6_x: float = -20.0
    v6_y: float = 4.0


class SimulationCreate(SimulationBase):
    pass


class Simulation(SimulationBase):
    id: int
    created_at: datetime
    status: str

    class Config:
        orm_mode = True


class SimulationInfo(BaseModel):
    id: int
    created_at: datetime
    status: str
    input_parameters: Dict[str, Any]

    class Config:
        orm_mode = True


class SimulationResultBase(BaseModel):
    results_data: Dict[str, Any]
    summary_data: Dict[str, Any]


class SimulationResultCreate(SimulationResultBase):
    simulation_id: int


class SimulationResult(SimulationResultBase):
    id: int
    simulation_id: int

    class Config:
        orm_mode = True
