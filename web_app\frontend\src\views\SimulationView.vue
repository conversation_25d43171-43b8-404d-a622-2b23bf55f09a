<template>
  <el-container>
    <el-aside width="350px">
      <h2>Simulation Parameters</h2>
      <InputForm @run-simulation="handleRunSimulation" :is-running="isRunning" />
      <HistoryView @select-simulation="loadSimulation" />
    </el-aside>
    <el-main v-loading="isRunning" element-loading-text="Simulation in progress...">
      <h2>Results</h2>
      <ResultsDashboard v-if="simulationData && simulationData.theta" ref="resultsDashboard" :simulationData="simulationData" :simulationId="simulationId" />
      <el-empty v-else description="Run a simulation or select one from history"></el-empty>
    </el-main>
  </el-container>
</template>

<script>
import { ref, nextTick } from 'vue';
import { ElNotification } from 'element-plus';
import InputForm from '../components/InputForm.vue';
import ResultsDashboard from '../components/ResultsDashboard.vue';
import HistoryView from './HistoryView.vue';
import apiClient from '../api/client';

export default {
  name: 'SimulationView',
  components: {
    InputForm,
    ResultsDashboard,
    HistoryView,
  },
  setup() {
    const simulationData = ref(null);
    const simulationStatus = ref('');
    const simulationId = ref(null);
    const isRunning = ref(false);
    const resultsDashboard = ref(null);

    const handleRunSimulation = async (params) => {
      try {
        simulationData.value = null;
        isRunning.value = true;
        simulationStatus.value = 'running';
        const response = await apiClient.startSimulation(params);
        simulationId.value = response.data.id;
        ElNotification({
          title: 'Success',
          message: `Simulation started with ID: ${simulationId.value}`,
          type: 'success',
        });
        pollStatus(simulationId.value);
      } catch (error) {
        console.error('Failed to start simulation:', error);
        simulationStatus.value = 'failed';
        isRunning.value = false;
        ElNotification({
          title: 'Error',
          message: 'Failed to start simulation.',
          type: 'error',
        });
      }
    };

    const fetchResults = async (id) => {
      try {
        const response = await apiClient.getSimulationResults(id);
        simulationData.value = response.data;
        await nextTick();
        if (resultsDashboard.value) {
          resultsDashboard.value.initializeAnimation();
        }
        ElNotification({
          title: 'Success',
          message: 'Simulation results loaded.',
          type: 'success',
        });
      } catch (error) {
        console.error('Failed to fetch results:', error);
        ElNotification({
          title: 'Error',
          message: 'Failed to fetch results.',
          type: 'error',
        });
      }
    };

    const pollStatus = (id) => {
      const interval = setInterval(async () => {
        try {
          const response = await apiClient.getSimulationStatus(id);
          if (response.data.status === 'completed') {
            clearInterval(interval);
            simulationStatus.value = 'completed';
            isRunning.value = false;
            await fetchResults(id);
          } else if (response.data.status === 'failed') {
            clearInterval(interval);
            simulationStatus.value = 'failed';
            isRunning.value = false;
            ElNotification({
              title: 'Error',
              message: 'Simulation failed to complete.',
              type: 'error',
            });
          }
        } catch (error) {
          clearInterval(interval);
          console.error('Failed to poll status:', error);
          simulationStatus.value = 'failed';
          isRunning.value = false;
          ElNotification({
            title: 'Error',
            message: 'Failed to get simulation status.',
            type: 'error',
          });
        }
      }, 2000); // Poll every 2 seconds
    };

    const loadSimulation = async (id) => {
      simulationId.value = id;
      await fetchResults(id);
    };

    return {
      simulationData,
      simulationStatus,
      simulationId,
      isRunning,
      handleRunSimulation,
      loadSimulation,
      resultsDashboard,
    };
  },
};
</script>

<style scoped>
.el-container {
  height: 100vh;
}
.el-aside {
  padding: 20px;
  border-right: 1px solid #ebeef5;
}
.el-main {
  padding: 20px;
}
</style>
