<template>
  <div class="industrial-layout">
    <el-container class="main-container">
      <el-aside width="380px" class="control-panel">
        <div class="panel-header">
          <h2 class="panel-title">
            <el-icon class="title-icon"><Setting /></el-icon>
            仿真参数配置
          </h2>
        </div>
        <div class="panel-content">
          <InputForm @run-simulation="handleRunSimulation" :is-running="isRunning" />
          <HistoryView @select-simulation="loadSimulation" />
        </div>
      </el-aside>
      <el-main
        v-loading="isRunning"
        element-loading-text="仿真计算中..."
        element-loading-background="rgba(45, 52, 54, 0.8)"
        element-loading-spinner="el-icon-loading"
        class="results-area"
      >
        <div class="results-header">
          <h2 class="results-title">
            <el-icon class="title-icon"><DataAnalysis /></el-icon>
            仿真结果分析
          </h2>
          <div class="status-indicator" :class="{ 'running': isRunning, 'completed': simulationData }">
            <span class="status-dot"></span>
            <span class="status-text">
              {{ isRunning ? '计算中' : simulationData ? '已完成' : '待运行' }}
            </span>
          </div>
        </div>
        <div class="results-content">
          <ResultsDashboard
            v-if="simulationData && simulationData.theta"
            ref="resultsDashboard"
            :simulationData="simulationData"
            :simulationId="simulationId"
          />
          <el-empty
            v-else
            description="请配置参数并运行仿真，或从历史记录中选择一个仿真结果"
            class="empty-state"
          >
            <template #image>
              <el-icon class="empty-icon"><Monitor /></el-icon>
            </template>
          </el-empty>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { ref, nextTick } from 'vue';
import { ElNotification } from 'element-plus';
import { Setting, DataAnalysis, Monitor } from '@element-plus/icons-vue';
import InputForm from '../components/InputForm.vue';
import ResultsDashboard from '../components/ResultsDashboard.vue';
import HistoryView from './HistoryView.vue';
import apiClient from '../api/client';

export default {
  name: 'SimulationView',
  components: {
    InputForm,
    ResultsDashboard,
    HistoryView,
    Setting,
    DataAnalysis,
    Monitor,
  },
  setup() {
    const simulationData = ref(null);
    const simulationStatus = ref('');
    const simulationId = ref(null);
    const isRunning = ref(false);
    const resultsDashboard = ref(null);

    const handleRunSimulation = async (params) => {
      try {
        simulationData.value = null;
        isRunning.value = true;
        simulationStatus.value = 'running';
        const response = await apiClient.startSimulation(params);
        simulationId.value = response.data.id;
        ElNotification({
          title: '仿真启动成功',
          message: `仿真任务已创建，ID: ${simulationId.value}`,
          type: 'success',
        });
        pollStatus(simulationId.value);
      } catch (error) {
        console.error('Failed to start simulation:', error);
        simulationStatus.value = 'failed';
        isRunning.value = false;
        ElNotification({
          title: 'Error',
          message: 'Failed to start simulation.',
          type: 'error',
        });
      }
    };

    const fetchResults = async (id) => {
      try {
        const response = await apiClient.getSimulationResults(id);
        simulationData.value = response.data;
        await nextTick();
        if (resultsDashboard.value) {
          resultsDashboard.value.initializeAnimation();
        }
        ElNotification({
          title: 'Success',
          message: 'Simulation results loaded.',
          type: 'success',
        });
      } catch (error) {
        console.error('Failed to fetch results:', error);
        ElNotification({
          title: 'Error',
          message: 'Failed to fetch results.',
          type: 'error',
        });
      }
    };

    const pollStatus = (id) => {
      const interval = setInterval(async () => {
        try {
          const response = await apiClient.getSimulationStatus(id);
          if (response.data.status === 'completed') {
            clearInterval(interval);
            simulationStatus.value = 'completed';
            isRunning.value = false;
            await fetchResults(id);
          } else if (response.data.status === 'failed') {
            clearInterval(interval);
            simulationStatus.value = 'failed';
            isRunning.value = false;
            ElNotification({
              title: 'Error',
              message: 'Simulation failed to complete.',
              type: 'error',
            });
          }
        } catch (error) {
          clearInterval(interval);
          console.error('Failed to poll status:', error);
          simulationStatus.value = 'failed';
          isRunning.value = false;
          ElNotification({
            title: 'Error',
            message: 'Failed to get simulation status.',
            type: 'error',
          });
        }
      }, 2000); // Poll every 2 seconds
    };

    const loadSimulation = async (id) => {
      simulationId.value = id;
      await fetchResults(id);
    };

    return {
      simulationData,
      simulationStatus,
      simulationId,
      isRunning,
      handleRunSimulation,
      loadSimulation,
      resultsDashboard,
    };
  },
};
</script>

<style scoped>
/* 工业风格主题 */
.industrial-layout {
  background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  min-height: 100vh;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

.main-container {
  height: 100vh;
  background: transparent;
}

/* 控制面板样式 */
.control-panel {
  background: linear-gradient(180deg, #2d3436 0%, #636e72 100%);
  border-right: 3px solid #74b9ff;
  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.3);
  position: relative;
}

.control-panel::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, #74b9ff 0%, #0984e3 50%, #74b9ff 100%);
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

.panel-header {
  padding: 25px 20px 15px;
  border-bottom: 2px solid rgba(116, 185, 255, 0.3);
  background: rgba(0, 0, 0, 0.2);
}

.panel-title {
  color: #74b9ff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  text-shadow: 0 0 10px rgba(116, 185, 255, 0.5);
}

.title-icon {
  margin-right: 10px;
  font-size: 20px;
}

.panel-content {
  padding: 20px;
  height: calc(100vh - 100px);
  overflow-y: auto;
}

/* 结果区域样式 */
.results-area {
  background: linear-gradient(135deg, #dfe6e9 0%, #b2bec3 100%);
  padding: 0;
  position: relative;
}

.results-header {
  background: linear-gradient(90deg, #2d3436 0%, #636e72 100%);
  padding: 20px 30px;
  border-bottom: 3px solid #00b894;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.results-title {
  color: #00b894;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  text-shadow: 0 0 10px rgba(0, 184, 148, 0.5);
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  background: #636e72;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-indicator.running .status-dot {
  background: #fdcb6e;
  box-shadow: 0 0 10px rgba(253, 203, 110, 0.8);
}

.status-indicator.completed .status-dot {
  background: #00b894;
  box-shadow: 0 0 10px rgba(0, 184, 148, 0.8);
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.status-text {
  color: #dfe6e9;
  font-size: 14px;
  font-weight: 500;
}

.results-content {
  padding: 25px;
  height: calc(100vh - 120px);
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
}

/* 空状态样式 */
.empty-state {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px dashed #b2bec3;
}

.empty-icon {
  font-size: 64px;
  color: #636e72;
  margin-bottom: 20px;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.results-content::-webkit-scrollbar {
  width: 8px;
}

.panel-content::-webkit-scrollbar-track,
.results-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb,
.results-content::-webkit-scrollbar-thumb {
  background: rgba(116, 185, 255, 0.6);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.results-content::-webkit-scrollbar-thumb:hover {
  background: rgba(116, 185, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    width: 320px !important;
  }

  .panel-title {
    font-size: 16px;
  }

  .results-title {
    font-size: 18px;
  }
}
</style>
