#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六边形卷针仿真核心类
包含主要的仿真逻辑和数据管理
"""

import numpy as np
from geometry_utils import GeometryUtils
from thickness_calculator import ThicknessCalculator
from contact_calculator import ContactCalculator
from wrapping_calculator import WrappingCalculator
from rounded_corner_calculator import RoundedCornerCalculator
from visualization import Visualizer
from animation import AnimationCreator


class HexagonSimulation:
    """六边形卷针仿真类"""

    def __init__(
        self,
        rotation_center_x_offset=2.0,
        film_thickness=0.1,
        total_rotation=3600,
        step_angle=0.5,
        use_rounded_corners=False,
        sharp_radius=0.8,
        blunt_radius=12.0,
    ):
        """
        初始化仿真参数

        Parameters:
        rotation_center_x_offset: float - 旋转中心X偏移
        film_thickness: float - 隔膜厚度
        total_rotation: float - 总旋转角度
        step_angle: float - 角度步长
        use_rounded_corners: bool - 是否使用圆角
        sharp_radius: float - 锐角圆角半径
        blunt_radius: float - 钝角圆角半径
        """
        # 基本参数
        self.rotation_center_x_offset = rotation_center_x_offset
        self.film_thickness = film_thickness
        self.total_rotation = total_rotation
        self.step_angle = step_angle

        # 圆角参数
        self.use_rounded_corners = use_rounded_corners
        self.sharp_radius = sharp_radius
        self.blunt_radius = blunt_radius

        # 初始化几何参数
        self._initialize_geometry()

        # 初始化角度数组
        self._initialize_angles()

        # 初始化计算器
        self._initialize_calculators()

        # 初始化结果数组
        self._initialize_results()

    def _calculate_initial_geometry_parameters(self):
        """
        计算初始几何参数：边长、圆弧长度、upper_point到V5切点的距离
        基于main_coordinate_tangent_analysis.py的实现
        """
        print("计算初始几何参数...")

        # 计算所有边长（切点到切点）
        edge_lengths = []
        all_tangent_points = []

        # 获取内切圆圆心
        if self.use_rounded_corners:
            circle_centers = self.rounded_corner_calc.circle_centers
        else:
            # 尖角模式：需要计算内切圆圆心
            circle_centers = self._calculate_incircle_centers()

        # 计算所有内切圆的切点
        for i in range(len(circle_centers)):
            center = circle_centers[i]
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius

            # 使用正确的边定义计算切点
            n = len(self.original_vertices)

            # 前一条边：从前一个顶点到当前顶点
            prev_vertex = self.original_vertices[(i - 1) % n]
            current_vertex = self.original_vertices[i]
            next_vertex = self.original_vertices[(i + 1) % n]

            # 计算两条边上的切点
            tangent_point1 = self._calculate_tangent_point_on_line(
                center, radius, prev_vertex, current_vertex
            )
            tangent_point2 = self._calculate_tangent_point_on_line(
                center, radius, current_vertex, next_vertex
            )

            all_tangent_points.append((tangent_point1, tangent_point2))

        # 计算相邻切点之间的距离
        for i in range(len(all_tangent_points)):
            current_end_point = all_tangent_points[i][1]
            next_start_point = all_tangent_points[(i + 1) % len(all_tangent_points)][0]
            edge_length = np.linalg.norm(next_start_point - current_end_point)
            edge_lengths.append(edge_length)

        # 计算所有圆弧长度
        arc_lengths = []
        for i in range(len(circle_centers)):
            center = circle_centers[i]
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius

            # 使用已计算的切点
            tangent_point1, tangent_point2 = all_tangent_points[i]

            # 计算切点相对于圆心的角度
            to_tangent1 = tangent_point1 - center
            to_tangent2 = tangent_point2 - center

            angle1 = np.arctan2(to_tangent1[1], to_tangent1[0])
            angle2 = np.arctan2(to_tangent2[1], to_tangent2[0])

            # 确保角度在[0, 2π]范围内
            angle1 = angle1 % (2 * np.pi)
            angle2 = angle2 % (2 * np.pi)

            # 确定起始和结束角度（按逆时针方向）
            if angle2 < angle1:
                angle2 += 2 * np.pi

            arc_angle = angle2 - angle1
            arc_length = radius * arc_angle
            arc_lengths.append(arc_length)

        # 计算upper_point到V5切点的距离
        # V5是索引4，获取V5的第一个切点（起始切点）
        v5_start_tangent = all_tangent_points[4][1]  # V5的起始切点
        upper_to_v5_distance = np.linalg.norm(self.upper_point - v5_start_tangent)

        geometry_params = {
            "edge_lengths": edge_lengths,
            "arc_lengths": arc_lengths,
            "all_tangent_points": all_tangent_points,
            "upper_to_v5_distance": upper_to_v5_distance,
            "vertex_names": ["V1", "V2", "V3", "V4", "V5", "V6"],
        }

        print(f"边长: {[f'{l:.2f}' for l in edge_lengths]} mm")  # noqa
        print(f"圆弧长度: {[f'{l:.2f}' for l in arc_lengths]} mm")  # noqa
        print(
            f"Upper点({self.upper_point[0]:.1f}, {self.upper_point[1]:.1f})到V5切点距离: {upper_to_v5_distance:.2f} mm"
        )

        return geometry_params

    def _calculate_incircle_centers(self):
        """计算内切圆圆心（尖角模式）"""
        centers = []
        for i in range(len(self.original_vertices)):
            # 计算顶点角度
            angle = self._calculate_vertex_angle(self.original_vertices, i)

            # 根据角度大小选择半径
            if angle < 90:
                radius = self.sharp_radius
            else:
                radius = self.blunt_radius

            # 计算圆心
            center = self._calculate_incircle_center(self.original_vertices, i, radius)
            centers.append(center)

        return np.array(centers)

    def _calculate_vertex_angle(self, vertices, vertex_index):
        """计算顶点的内角"""
        n = len(vertices)
        prev_vertex = vertices[(vertex_index - 1) % n]
        current_vertex = vertices[vertex_index]
        next_vertex = vertices[(vertex_index + 1) % n]

        # 计算两个边向量
        vec1 = prev_vertex - current_vertex
        vec2 = next_vertex - current_vertex

        # 计算角度
        dot_product = np.dot(vec1, vec2)
        norms = np.linalg.norm(vec1) * np.linalg.norm(vec2)

        if norms == 0:
            return 0

        cos_angle = np.clip(dot_product / norms, -1, 1)
        angle_rad = np.arccos(cos_angle)
        angle_deg = np.rad2deg(angle_rad)

        return angle_deg

    def _calculate_incircle_center(self, vertices, vertex_index, radius):
        """计算单个顶点的内切圆圆心"""
        n = len(vertices)
        prev_vertex = vertices[(vertex_index - 1) % n]
        current_vertex = vertices[vertex_index]
        next_vertex = vertices[(vertex_index + 1) % n]

        # 计算两个边向量
        vec1 = prev_vertex - current_vertex
        vec2 = next_vertex - current_vertex

        # 归一化边向量
        vec1_norm = vec1 / np.linalg.norm(vec1)
        vec2_norm = vec2 / np.linalg.norm(vec2)

        # 计算角平分线方向
        bisector = vec1_norm + vec2_norm
        bisector_norm = bisector / np.linalg.norm(bisector)

        # 计算内角的一半
        angle = self._calculate_vertex_angle(vertices, vertex_index)
        half_angle_rad = np.deg2rad(angle / 2)

        # 计算圆心到顶点的距离
        distance = radius / np.sin(half_angle_rad)

        # 计算圆心位置
        center = current_vertex + bisector_norm * distance

        return center

    def _calculate_tangent_point_on_line(self, center, radius, point1, point2):
        """计算内切圆与直线的切点"""
        # 直线方向向量
        line_vec = point2 - point1
        line_length = np.linalg.norm(line_vec)

        if line_length == 0:
            return point1

        # 单位方向向量
        line_unit = line_vec / line_length

        # 从point1到圆心的向量
        to_center = center - point1

        # 投影长度
        proj_length = np.dot(to_center, line_unit)

        # 限制投影点在线段范围内
        proj_length = np.clip(proj_length, 0, line_length)

        # 投影点（垂足）就是切点
        tangent_point = point1 + proj_length * line_unit

        return tangent_point

    def _get_edge_increment(self, prev_contact_type, current_contact_type):
        """
        根据接触点切换获取对应的边长增量
        基于main_coordinate_tangent_analysis.py的实现
        """
        # 定义顶点切换的边长映射
        # edge_lengths的顺序是：[V1→V2, V2→V3, V3→V4, V4→V5, V5→V6, V6→V1]
        edge_map = {
            ("V5", "V4"): 3,  # V5 -> V4，对应V4→V5的边长（索引3）
            ("V4", "V3"): 2,  # V4 -> V3，对应V3→V4的边长（索引2）
            ("V3", "V2"): 1,  # V3 -> V2，对应V2→V3的边长（索引1）
            ("V2", "V1"): 0,  # V2 -> V1，对应V1→V2的边长（索引0）
            ("V1", "V6"): 5,  # V1 -> V6，对应V6→V1的边长（索引5）
            ("V6", "V5"): 4,  # V6 -> V5，对应V5→V6的边长（索引4）
        }

        # 提取顶点名称
        def extract_vertex(contact_type):
            if "V1" in contact_type:
                return "V1"
            elif "V2" in contact_type:
                return "V2"
            elif "V3" in contact_type:
                return "V3"
            elif "V4" in contact_type:
                return "V4"
            elif "V5" in contact_type:
                return "V5"
            elif "V6" in contact_type:
                return "V6"
            return None

        prev_vertex = extract_vertex(prev_contact_type)
        current_vertex = extract_vertex(current_contact_type)

        if prev_vertex and current_vertex:
            edge_key = (prev_vertex, current_vertex)
            if edge_key in edge_map:
                edge_index = edge_map[edge_key]
                return self.initial_geometry["edge_lengths"][edge_index]

        return 0.0  # 未找到对应的边长

    def _initialize_geometry(self):
        """初始化几何参数"""
        # 六边形顶点坐标（原始）
        self.original_vertices = np.array(
            [
                [-30, 0],  # V1
                [-20, -4],  # V2
                [20, -4],  # V3
                [30, 0],  # V4
                [20, 4],  # V5
                [-20, 4],  # V6
            ]
        )

        # 当前顶点坐标（会随厚度变化）
        self.vertices = self.original_vertices.copy()

        # 几何中心
        self.geometric_center = np.mean(self.original_vertices, axis=0)

        # 旋转中心
        self.rotation_center = np.array(
            [
                self.geometric_center[0]
                + self.rotation_center_x_offset,  # X = 几何中心X + 偏移
                self.geometric_center[1],  # Y = 几何中心Y (不变)
            ]
        )

        # 过辊参数（保持原有命名）
        self.A = np.array([0.5, 80.0])  # 过辊A [X坐标, Y坐标] - 可直接修改
        self.B = np.array([-30.0, 80.0])  # 过辊B [X坐标, Y坐标] - 可直接修改
        self.roller_A = self.A  # 兼容性别名
        self.roller_radius = 2.0

        # 双过辊系统的固定点
        self.contact_A_entry = np.array([0.5, 82.0])  # A点入口
        self.contact_B_exit = np.array([-30.0, 82.0])  # B点出口

        # 上下方点（绝对坐标）
        self.upper_point = np.array(
            [self.geometric_center[0] + self.rotation_center_x_offset, 4]
        )  # 旋转中心上方点
        self.lower_point = np.array(
            [self.geometric_center[0] + self.rotation_center_x_offset, -4]
        )  # 旋转中心下方点

        # 上下方点（相对于六边形，兼容性）
        self.upper_point_relative = np.array([2, 4])
        self.lower_point_relative = np.array([2, -4])

    def _initialize_angles(self):
        """初始化角度数组"""
        self.theta_deg = np.arange(
            0, self.total_rotation + self.step_angle, self.step_angle
        )
        self.theta = np.deg2rad(self.theta_deg)

    def _initialize_calculators(self):
        """初始化各种计算器"""
        # 圆角计算器（如果启用）
        if self.use_rounded_corners:
            self.rounded_corner_calc = RoundedCornerCalculator(
                self.original_vertices, self.sharp_radius, self.blunt_radius
            )
        else:
            self.rounded_corner_calc = None

        # 厚度计算器（传入圆角计算器）
        self.thickness_calc = ThicknessCalculator(
            self.original_vertices, self.rounded_corner_calc
        )

        # 接触点计算器（传入圆角计算器）
        self.contact_calc = ContactCalculator(
            self.A, self.roller_radius, self.rounded_corner_calc
        )

        # 包覆计算器（传入圆角计算器）
        self.wrapping_calc = WrappingCalculator(
            self.original_vertices, self.rounded_corner_calc
        )

        # 计算初始几何参数（边长、圆弧长度等）
        self.initial_geometry = self._calculate_initial_geometry_parameters()

        self.visualizer = Visualizer()
        self.animator = AnimationCreator(self)

    def _initialize_results(self):
        """初始化结果数组"""
        n_points = len(self.theta)

        # 主要结果（保持原版定义）
        self.L = np.zeros(n_points)  # 过辊到卷针长度（切线长度）
        self.S = np.zeros(n_points)  # 包覆长度（仅卷针表面）
        self.S_total = np.zeros(n_points)  # 总薄膜长度（切线+包覆）

        # 接触点相关
        self.contact_points = np.zeros((n_points, 2))
        self.contact_type = [""] * n_points
        self.roller_contact_points = np.zeros((n_points, 2))
        self.roller_contact_distances = np.zeros(n_points)

        # 轨迹点
        self.upper_point_trajectory = np.zeros((n_points, 2))
        self.lower_point_trajectory = np.zeros((n_points, 2))

        # 厚度相关
        self.layer_numbers = np.zeros(n_points, dtype=int)
        self.accumulated_thickness = np.zeros(n_points)
        self.layer_consumption = np.zeros(n_points)

        # 详细分析数据
        self.tangent_A_lengths = np.zeros(n_points)
        self.tangent_AB_lengths = np.zeros(n_points)
        self.arc_A_lengths = np.zeros(n_points)
        self.arc_B_lengths = np.zeros(n_points)
        self.contact_A_points = np.zeros((n_points, 2))
        self.contact_B_points = np.zeros((n_points, 2))
        self.contact_A_entry_points = np.zeros((n_points, 2))

    def calculate_layer_from_angle(self, angle_deg):
        """
        根据角度计算层数和累积厚度
        基于main_coordinate_tangent_analysis.py的逻辑：第一圈使用原始几何，第二圈累积一层厚度

        Parameters:
        angle_deg: float - 角度（度）

        Returns:
        layer_number: int - 层数
        accumulated_thickness: float - 累积厚度
        """
        # 基于角度计算圈数（从0开始）
        layer_number = int(angle_deg // 360)

        # 厚度计算：第一圈(0-360°)使用原始几何，第二圈(360-720°)累积一层厚度
        # 这与main_coordinate_tangent_analysis.py的逻辑完全一致
        accumulated_thickness = layer_number * self.film_thickness

        return layer_number, accumulated_thickness

    def _extract_vertex_index(self, contact_type):
        """
        从接触点类型中提取顶点索引

        Parameters:
        contact_type: str or dict - 接触点类型

        Returns:
        vertex_index: int or None - 顶点索引（0-5）
        """
        contact_str = (
            contact_type["type"]
            if isinstance(contact_type, dict)
            else str(contact_type)
        )

        if "V" in contact_str:
            try:
                # 提取V后面的数字，转换为索引（V1->0, V2->1, etc.）
                vertex_num = int(
                    contact_str.split("V")[1].split("_")[0]
                )  # 处理可能的后缀
                return vertex_num - 1
            except (ValueError, IndexError):
                return None
        return None

    def _is_same_physical_contact_point(self, prev_contact_type, current_contact_type):
        """
        判断两个接触点类型是否指向同一个物理点

        Parameters:
        prev_contact_type: str - 前一个接触点类型
        current_contact_type: str - 当前接触点类型

        Returns:
        bool - 是否为同一个物理点
        """
        # 如果字符串完全相同，肯定是同一个点
        if prev_contact_type == current_contact_type:
            return True

        # 提取顶点索引进行比较
        prev_vertex_idx = self._extract_vertex_index(prev_contact_type)
        current_vertex_idx = self._extract_vertex_index(current_contact_type)

        # 如果都是顶点且索引相同，则是同一个物理点
        if prev_vertex_idx is not None and current_vertex_idx is not None:
            return prev_vertex_idx == current_vertex_idx

        # 检查是否都指向upper_point
        prev_is_upper = any(keyword in prev_contact_type for keyword in
                           ["upper_point", "fixed_upper_point", "leftmost_upper_point"])
        current_is_upper = any(keyword in current_contact_type for keyword in
                              ["upper_point", "fixed_upper_point", "leftmost_upper_point"])

        if prev_is_upper and current_is_upper:
            return True

        return False

    def rotate_vertices_around_center_with_custom_vertices(
        self, theta, custom_vertices
    ):
        """
        使用自定义顶点进行旋转

        Parameters:
        theta: float - 旋转角度（弧度）
        custom_vertices: np.array - 自定义顶点坐标

        Returns:
        rotated_vertices: np.array - 旋转后的顶点坐标
        """
        return GeometryUtils.rotate_points_around_center(
            custom_vertices, self.rotation_center, theta
        )

    def run_simulation(self):
        """运行完整仿真"""
        print("开始运行六边形卷针仿真...")
        print(f"总角度: {self.total_rotation}°, 步长: {self.step_angle}°")
        print(f"数据点数量: {len(self.theta)}")

        prev_contact_type = None

        # 包覆长度计算变量（基于main_coordinate_tangent_analysis.py）
        fixed_wrapping_length = 0.0  # 已完全包覆的固定长度
        current_arc_length = 0.0  # 当前圆弧的覆盖长度

        for i, angle_deg in enumerate(self.theta_deg):
            # 显示进度
            if i % 100 == 0:
                progress = (i / len(self.theta_deg)) * 100
                print(f"进度: {progress:.1f}% ({i}/{len(self.theta_deg)})")

            # 计算当前层数和厚度
            layer_num, accum_thickness = self.calculate_layer_from_angle(angle_deg)
            self.layer_numbers[i] = layer_num
            self.accumulated_thickness[i] = accum_thickness

            # 计算每层消耗长度
            self.layer_consumption[i] = self.thickness_calc.calculate_layer_consumption(
                layer_num, self.film_thickness
            )

            # 检查是否需要更新厚度（只在圈数变化时更新，类似main_coordinate_tangent_analysis.py）
            need_thickness_update = False
            if i == 0:
                need_thickness_update = True
                current_thickness_for_geometry = accum_thickness
            else:
                prev_layer = self.layer_numbers[i - 1]
                if layer_num != prev_layer:
                    need_thickness_update = True
                    current_thickness_for_geometry = accum_thickness
                    print(
                        f"角度{angle_deg}°: 进入第{layer_num + 1}圈，累积厚度: {accum_thickness:.3f}mm"
                    )
                else:
                    # 使用之前的厚度设置
                    current_thickness_for_geometry = accum_thickness

            # 更新顶点位置（只在厚度变化时重新计算）
            if need_thickness_update and current_thickness_for_geometry > 0:
                if self.use_rounded_corners:
                    # 圆角模式：使用圆角计算器的厚度影响逻辑
                    geometric_vertices, _ = (
                        self.rounded_corner_calc.apply_thickness_impact(
                            current_thickness_for_geometry
                        )
                    )
                    physical_vertices = geometric_vertices.copy()  # 圆角模式下两者相同
                    current_vertices = geometric_vertices
                    self.vertices = geometric_vertices
                else:
                    # 尖角模式：使用原有逻辑
                    # 几何精确方法：用于周长和包覆长度计算
                    geometric_vertices = self.thickness_calc.update_vertices_geometric(
                        current_thickness_for_geometry
                    )
                    # 物理真实方法：用于接触点计算
                    physical_vertices = self.thickness_calc.update_vertices_physical(
                        current_thickness_for_geometry
                    )
                    current_vertices = geometric_vertices
                    self.vertices = geometric_vertices
            elif need_thickness_update:
                # 厚度为0或首次计算
                geometric_vertices = self.original_vertices.copy()
                physical_vertices = self.original_vertices.copy()
                current_vertices = self.original_vertices.copy()
                self.vertices = self.original_vertices.copy()

                # 重置圆角计算器的厚度
                if self.use_rounded_corners:
                    self.rounded_corner_calc.apply_thickness_impact(0.0)
            else:
                # 使用之前计算的顶点（避免重复计算）
                current_vertices = self.vertices.copy()
                geometric_vertices = current_vertices
                physical_vertices = current_vertices

            # 旋转顶点
            rotated_physical_vertices = (
                self.rotate_vertices_around_center_with_custom_vertices(
                    self.theta[i], physical_vertices
                )
            )
            rotated_vertices = self.rotate_vertices_around_center_with_custom_vertices(
                self.theta[i], current_vertices
            )

            # 旋转上下方点
            rotated_upper = GeometryUtils.rotate_points_around_center(
                self.upper_point.reshape(1, -1), self.rotation_center, self.theta[i]
            )[0]
            rotated_lower = GeometryUtils.rotate_points_around_center(
                self.lower_point.reshape(1, -1), self.rotation_center, self.theta[i]
            )[0]

            # 记录轨迹
            self.upper_point_trajectory[i] = rotated_upper
            self.lower_point_trajectory[i] = rotated_lower

            # 寻找接触点（使用物理方法更新的顶点）
            contact, contact_type = self.contact_calc.find_leftmost_valid_contact_point(
                rotated_physical_vertices, rotated_upper, angle_deg
            )

            self.contact_points[i] = contact
            self.contact_type[i] = contact_type

            # 计算过辊接触点
            roller_contact = self.contact_calc.find_roller_contact_point(contact)
            self.roller_contact_points[i] = roller_contact

            # 计算切线长度（过辊A到接触点）
            if (
                self.use_rounded_corners
                and self.contact_calc.last_contact_info is not None
            ):
                # 圆角模式：使用公切线长度
                _, tangent_length, _ = (
                    self.contact_calc.calculate_rounded_wrapping_length()
                )
            else:
                # 尖角模式：使用直线距离
                tangent_length = np.linalg.norm(roller_contact - contact)

            self.roller_contact_distances[i] = tangent_length
            self.L[i] = tangent_length  # L存储切线长度，与原版一致

            # 计算包覆长度（使用预计算的几何参数）
            if i > 0:
                # 检查是否切换接触点（基于物理点位而非字符串比较）
                is_physical_switch = not self._is_same_physical_contact_point(
                    prev_contact_type, contact_type
                )

                if is_physical_switch:
                    # 接触点切换时的包覆长度计算
                    if prev_contact_type in [
                        "fixed_upper_point",
                        "leftmost_upper_point",
                    ] and "V" in str(contact_type):
                        # 第一次从upper_point切换到V5：使用预计算的距离
                        fixed_wrapping_length = self.initial_geometry[
                            "upper_to_v5_distance"
                        ]
                        current_arc_length = 0.0
                        print(
                            f"角度{angle_deg}°: 首次包覆 {prev_contact_type} -> {contact_type}, 固定长度: {fixed_wrapping_length:.2f}mm"
                        )
                    else:
                        # 其他接触点切换：使用预计算的完整圆弧长度
                        arc_increment = 0.0
                        if "V" in str(prev_contact_type):
                            # 从initial_geometry的arc_lengths中获取上一个顶点的完整圆弧长度
                            prev_vertex_index = self._extract_vertex_index(
                                prev_contact_type
                            )
                            if prev_vertex_index is not None:
                                # 使用预计算的完整圆弧长度
                                arc_increment = float(
                                    self.initial_geometry["arc_lengths"][
                                        prev_vertex_index
                                    ]
                                )
                            else:
                                # 如果不是从V顶点切换，使用当前圆弧长度
                                arc_increment = (
                                    current_arc_length
                                    if current_arc_length > 0
                                    else 0.0
                                )
                        else:
                            arc_increment = (
                                current_arc_length if current_arc_length > 0 else 0.0
                            )

                        # 使用预计算的边长增量
                        edge_increment = self._get_edge_increment(
                            prev_contact_type, contact_type
                        )

                        if edge_increment > 0:
                            fixed_wrapping_length += arc_increment + edge_increment
                            current_arc_length = 0.0  # 重置圆弧长度
                            print(
                                f"角度{angle_deg}°: 接触点切换 {prev_contact_type} -> {contact_type}, "
                                f"圆弧增量: {arc_increment:.3f}mm, 边长增量: {edge_increment:.2f}mm, "
                                f"固定长度: {fixed_wrapping_length:.2f}mm"
                            )
                        else:
                            # 备用：使用原始逻辑
                            increment = (
                                self.wrapping_calc.calculate_wrapping_length_increment(
                                    rotated_vertices,
                                    contact_type,
                                    prev_contact_type,
                                    self.contact_points[i - 1],
                                    contact,
                                    angle_deg,
                                    contact_calc=self.contact_calc,
                                )
                            )
                            fixed_wrapping_length += arc_increment + increment
                            current_arc_length = 0.0
                            print(
                                f"角度{angle_deg}°: 接触点切换 {prev_contact_type} -> {contact_type}, "
                                f"圆弧增量: {arc_increment:.3f}mm, 原始增量: {increment:.2f}mm, "
                                f"固定长度: {fixed_wrapping_length:.2f}mm"
                            )
                else:
                    # 同一接触点内：计算当前圆弧长度（逐步包裹）
                    if (
                        "V" in str(contact_type)
                        and self.contact_calc.last_contact_info is not None
                    ):
                        # 圆角模式：计算当前圆弧覆盖长度
                        arc_length, _, _ = (
                            self.contact_calc.calculate_rounded_wrapping_length()
                        )
                        current_arc_length = arc_length

                # 总包覆长度 = 固定长度 + 当前圆弧长度
                self.S[i] = fixed_wrapping_length + current_arc_length
            else:
                self.S[i] = 0.0  # 初始包覆长度为0
                fixed_wrapping_length = 0.0
                current_arc_length = 0.0

            # 计算总薄膜长度（包含双过辊系统）
            # 1. 切线A长度（过辊A到接触点）
            tangent_A_length = tangent_length

            # 2. 包覆长度（卷针表面）
            wrapping_length = self.S[i]

            # 3. 切线AB长度（过辊A到过辊B的直线距离）
            tangent_AB_length = np.linalg.norm(self.B - self.A)

            # 4. 圆弧A长度（过辊A上的圆弧，从入口到接触点）
            # 简化计算：假设圆弧角度为90度
            arc_A_length = (np.pi / 2) * self.roller_radius

            # 5. 圆弧B长度（过辊B上的圆弧，从切线到出口）
            # 简化计算：假设圆弧角度为90度
            arc_B_length = (np.pi / 2) * self.roller_radius

            # 总长度计算：考虑切线长度变化的补偿效应
            # 当包覆长度跳变增加时，切线长度会相应减少，总长度应该平滑变化
            if i == 0:
                # 初始总长度
                self.S_total[i] = (
                    tangent_A_length
                    + wrapping_length
                    + tangent_AB_length
                    + arc_A_length
                    + arc_B_length
                )
            else:
                # 计算增量：主要来自包覆长度增量，切线长度变化相对较小
                wrapping_increment = wrapping_length - (self.S[i - 1] if i > 0 else 0)
                tangent_increment = tangent_A_length - self.tangent_A_lengths[i - 1]

                # 总长度增量 = 包覆增量 + 切线增量（通常切线增量为负，部分抵消包覆增量）
                total_increment = wrapping_increment + tangent_increment

                # 确保总长度单调递增，但增量应该相对平滑
                self.S_total[i] = self.S_total[i - 1] + max(
                    total_increment, 0.001
                )  # 最小增量0.001mm

            # 存储详细分析数据
            self.tangent_A_lengths[i] = tangent_A_length
            self.tangent_AB_lengths[i] = tangent_AB_length
            self.arc_A_lengths[i] = arc_A_length
            self.arc_B_lengths[i] = arc_B_length
            self.contact_A_points[i] = roller_contact
            self.contact_B_points[i] = self.B  # 简化
            self.contact_A_entry_points[i] = self.contact_A_entry

            prev_contact_type = contact_type

        print("仿真完成!")
        self._print_summary()

    def _print_summary(self):
        """打印仿真结果摘要"""
        print("\n" + "=" * 60)
        print("仿真结果摘要")
        print("=" * 60)
        print(f"总旋转角度: {self.total_rotation}°")
        print(f"最终层数: {self.layer_numbers[-1]}")
        print(f"最大累积厚度: {self.accumulated_thickness[-1]:.3f}mm")
        print(f"最终切线长度: {self.L[-1]:.2f}mm")
        print(f"最终包覆长度: {self.S[-1]:.2f}mm")
        print(f"总消耗长度: {self.S_total[-1]:.2f}mm")
        print(f"平均每层消耗: {np.mean(self.layer_consumption):.3f}mm")
        print("=" * 60)

    def get_simulation_data(self):
        """获取仿真数据字典"""
        return {
            "theta_deg": self.theta_deg,
            "theta": self.theta,
            "L": self.L,
            "S": self.S,
            "S_total": self.S_total,
            "contact_points": self.contact_points,
            "contact_type": self.contact_type,
            "roller_contact_points": self.roller_contact_points,
            "upper_point_trajectory": self.upper_point_trajectory,
            "lower_point_trajectory": self.lower_point_trajectory,
            "layer_numbers": self.layer_numbers,
            "accumulated_thickness": self.accumulated_thickness,
            "layer_consumption": self.layer_consumption,
            "original_vertices": self.original_vertices,
            "rotation_center": self.rotation_center,
            "roller_A": self.A,
            "roller_radius": self.roller_radius,
        }

    def create_animation(self, **kwargs):
        """创建动画"""
        return self.animator.create_animation(**kwargs)

    def plot_results(self, **kwargs):
        """绘制结果图表"""
        data = self.get_simulation_data()
        data["sim_object"] = self  # 传入仿真对象
        self.visualizer.plot_simulation_results(data, **kwargs)

    def plot_contact_analysis(self, **kwargs):
        """绘制接触点分析"""
        data = self.get_simulation_data()
        self.visualizer.plot_contact_analysis(data, **kwargs)

    def plot_thickness_analysis(self, **kwargs):
        """绘制厚度分析"""
        data = self.get_simulation_data()
        self.visualizer.plot_thickness_analysis(data, **kwargs)

    def export_to_excel(self, file_path):
        """导出数据到Excel"""
        data = self.get_simulation_data()
        self.visualizer.export_data_to_excel(data, file_path)
