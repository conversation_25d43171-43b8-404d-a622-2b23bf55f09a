<template>
  <el-form :model="form" label-position="top">
    <el-form-item label="Winding Pin Shape">
      <el-select v-model="form.pin_shape" placeholder="Select">
        <el-option label="Hexagon" value="hexagon"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="Rotation Center X Offset (mm)">
      <el-input-number v-model="form.rotation_center_x_offset" :step="0.1" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Film Thickness (mm)">
      <el-input-number v-model="form.film_thickness" :step="0.01" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Total Rotation (°)">
      <el-input-number v-model="form.total_rotation" :step="360" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Step Angle (°)">
      <el-input-number v-model="form.step_angle" :step="0.1" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Use Rounded Corners">
      <el-switch v-model="form.use_rounded_corners"></el-switch>
    </el-form-item>
    <div v-if="form.use_rounded_corners">
      <el-form-item label="Sharp Corner Radius (mm)">
        <el-input-number v-model="form.sharp_radius" :step="0.1" controls-position="right"></el-input-number>
      </el-form-item>
      <el-form-item label="Blunt Corner Radius (mm)">
        <el-input-number v-model="form.blunt_radius" :step="0.1" controls-position="right"></el-input-number>
      </el-form-item>
    </div>
    <el-form-item label="Roller A Position X (mm)">
      <el-input-number v-model="form.roller_A_x" :step="1" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Roller A Position Y (mm)">
      <el-input-number v-model="form.roller_A_y" :step="1" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Roller B Position X (mm)">
      <el-input-number v-model="form.roller_B_x" :step="1" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Roller B Position Y (mm)">
      <el-input-number v-model="form.roller_B_y" :step="1" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="Roller Radius (mm)">
      <el-input-number v-model="form.roller_radius" :step="0.1" controls-position="right"></el-input-number>
    </el-form-item>

    <h4>Winding Pin Vertices (V1-V6)</h4>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="V1 X">
          <el-input-number v-model="form.v1_x" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V1 Y">
          <el-input-number v-model="form.v1_y" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V2 X">
          <el-input-number v-model="form.v2_x" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V2 Y">
          <el-input-number v-model="form.v2_y" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V3 X">
          <el-input-number v-model="form.v3_x" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V3 Y">
          <el-input-number v-model="form.v3_y" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V4 X">
          <el-input-number v-model="form.v4_x" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V4 Y">
          <el-input-number v-model="form.v4_y" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V5 X">
          <el-input-number v-model="form.v5_x" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V5 Y">
          <el-input-number v-model="form.v5_y" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V6 X">
          <el-input-number v-model="form.v6_x" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="V6 Y">
          <el-input-number v-model="form.v6_y" controls-position="right"></el-input-number>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item>
      <el-button type="primary" @click="runSimulation">Run Simulation</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { reactive } from 'vue';

export default {
  name: 'InputForm',
  emits: ['run-simulation'],
  setup(props, { emit }) {
    const form = reactive({
      pin_shape: 'hexagon',
      rotation_center_x_offset: 2.0,
      film_thickness: 0.1,
      total_rotation: 3600,
      step_angle: 0.5,
      use_rounded_corners: false,
      sharp_radius: 0.8,
      blunt_radius: 12.0,
      roller_A_x: 0,
      roller_A_y: 60,
      roller_B_x: -30,
      roller_B_y: 60,
      roller_radius: 2.0,
      v1_x: -30, v1_y: 0,
      v2_x: -20, v2_y: -4,
      v3_x: 20,  v3_y: -4,
      v4_x: 30,  v4_y: 0,
      v5_x: 20,  v5_y: 4,
      v6_x: -20, v6_y: 4,
    });

    const runSimulation = () => {
      emit('run-simulation', { ...form });
    };

    return {
      form,
      runSimulation,
    };
  },
};
</script>
