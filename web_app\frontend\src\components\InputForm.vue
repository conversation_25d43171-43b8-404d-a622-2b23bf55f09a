<template>
  <div class="industrial-form">
    <el-form :model="form" label-position="top" class="simulation-form">
      <!-- 基础参数 -->
      <div class="form-section">
        <h3 class="section-title">
          <el-icon><Setting /></el-icon>
          基础参数
        </h3>

        <el-form-item label="卷针形状" class="form-item">
          <el-select v-model="form.pin_shape" placeholder="选择卷针形状" class="industrial-select">
            <el-option label="六边形" value="hexagon"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="旋转中心X偏移 (mm)" class="form-item">
          <el-input-number
            v-model="form.rotation_center_x_offset"
            :step="0.1"
            :precision="1"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="隔膜厚度 (mm)" class="form-item">
          <el-input-number
            v-model="form.film_thickness"
            :step="0.01"
            :precision="2"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="总旋转角度 (°)" class="form-item">
          <el-input-number
            v-model="form.total_rotation"
            :step="360"
            :min="360"
            :max="7200"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="角度步长 (°)" class="form-item">
          <el-input-number
            v-model="form.step_angle"
            :step="0.1"
            :precision="1"
            :min="0.1"
            :max="5.0"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>
      </div>

      <!-- 圆角参数 -->
      <div class="form-section">
        <h3 class="section-title">
          <el-icon><Tools /></el-icon>
          圆角设置
        </h3>

        <el-form-item class="form-item">
          <template #label>
            <span class="switch-label">启用圆角仿真</span>
          </template>
          <el-switch
            v-model="form.use_rounded_corners"
            class="industrial-switch"
            active-color="#00b894"
            inactive-color="#636e72"
          ></el-switch>
        </el-form-item>

        <div v-if="form.use_rounded_corners" class="rounded-params">
          <el-form-item label="锐角圆角半径 (mm)" class="form-item">
            <el-input-number
              v-model="form.sharp_radius"
              :step="0.1"
              :precision="1"
              :min="0.1"
              controls-position="right"
              class="industrial-input"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="钝角圆角半径 (mm)" class="form-item">
            <el-input-number
              v-model="form.blunt_radius"
              :step="0.1"
              :precision="1"
              :min="0.1"
              controls-position="right"
              class="industrial-input"
            ></el-input-number>
          </el-form-item>
        </div>
      </div>

      <!-- 过辊参数 -->
      <div class="form-section">
        <h3 class="section-title">
          <el-icon><Operation /></el-icon>
          过辊配置
        </h3>

        <div class="roller-grid">
          <el-form-item label="过辊A位置X (mm)" class="form-item">
            <el-input-number
              v-model="form.roller_A_x"
              :step="0.5"
              :precision="1"
              controls-position="right"
              class="industrial-input"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="过辊A位置Y (mm)" class="form-item">
            <el-input-number
              v-model="form.roller_A_y"
              :step="0.5"
              :precision="1"
              controls-position="right"
              class="industrial-input"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="过辊B位置X (mm)" class="form-item">
            <el-input-number
              v-model="form.roller_B_x"
              :step="0.5"
              :precision="1"
              controls-position="right"
              class="industrial-input"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="过辊B位置Y (mm)" class="form-item">
            <el-input-number
              v-model="form.roller_B_y"
              :step="0.5"
              :precision="1"
              controls-position="right"
              class="industrial-input"
            ></el-input-number>
          </el-form-item>
        </div>

        <el-form-item label="过辊半径 (mm)" class="form-item">
          <el-input-number
            v-model="form.roller_radius"
            :step="0.1"
            :precision="1"
            :min="0.5"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>
      </div>

      <!-- 卷针顶点配置 -->
      <div class="form-section">
        <h3 class="section-title">
          <el-icon><Grid /></el-icon>
          卷针顶点坐标 (V1-V6)
        </h3>

        <div class="vertices-grid">
          <div class="vertex-group" v-for="i in 6" :key="i">
            <h4 class="vertex-title">顶点 V{{ i }}</h4>
            <div class="vertex-coords">
              <el-form-item :label="`X坐标`" class="coord-item">
                <el-input-number
                  v-model="form[`v${i}_x`]"
                  :step="0.5"
                  :precision="1"
                  controls-position="right"
                  class="industrial-input small"
                ></el-input-number>
              </el-form-item>

              <el-form-item :label="`Y坐标`" class="coord-item">
                <el-input-number
                  v-model="form[`v${i}_y`]"
                  :step="0.5"
                  :precision="1"
                  controls-position="right"
                  class="industrial-input small"
                ></el-input-number>
              </el-form-item>
            </div>
          </div>
        </div>
      </div>

      <!-- 运行按钮 -->
      <div class="form-section">
      <el-form-item class="run-button-container">
        <el-button
          type="primary"
          @click="runSimulation"
          :loading="isRunning"
          :disabled="isRunning"
          class="run-button"
          size="large"
        >
          <el-icon class="button-icon"><VideoPlay /></el-icon>
          {{ isRunning ? '仿真运行中...' : '开始仿真' }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { reactive } from 'vue';
import { Setting, Tools, Operation, Grid, VideoPlay } from '@element-plus/icons-vue';

export default {
  name: 'InputForm',
  components: {
    Setting,
    Tools,
    Operation,
    Grid,
    VideoPlay,
  },
  props: {
    isRunning: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['run-simulation'],
  setup(props, { emit }) {
    // 使用与main_new.py和hexagon_simulation.py一致的默认值
    const form = reactive({
      pin_shape: 'hexagon',
      rotation_center_x_offset: 2.0,  // 与main_new.py一致
      film_thickness: 0.1,             // 与hexagon_simulation.py一致
      total_rotation: 3600,            // 与hexagon_simulation.py一致
      step_angle: 0.5,                 // 与hexagon_simulation.py一致
      use_rounded_corners: false,      // 与main_new.py一致
      sharp_radius: 0.8,               // 与main_new.py一致
      blunt_radius: 12.0,              // 与main_new.py一致
      // 修正过辊位置，使用hexagon_simulation.py中的实际默认值
      roller_A_x: 0.5,                 // 与hexagon_simulation.py中self.A一致
      roller_A_y: 80.0,                // 与hexagon_simulation.py中self.A一致
      roller_B_x: -30.0,               // 与hexagon_simulation.py中self.B一致
      roller_B_y: 80.0,                // 与hexagon_simulation.py中self.B一致
      roller_radius: 2.0,              // 与hexagon_simulation.py一致
      // 六边形顶点坐标，与hexagon_simulation.py中original_vertices一致
      v1_x: -30, v1_y: 0,              // V1
      v2_x: -20, v2_y: -4,             // V2
      v3_x: 20,  v3_y: -4,             // V3
      v4_x: 30,  v4_y: 0,              // V4
      v5_x: 20,  v5_y: 4,              // V5
      v6_x: -20, v6_y: 4,              // V6
    });

    const runSimulation = () => {
      emit('run-simulation', { ...form });
    };

    return {
      form,
      runSimulation,
      isRunning: props.isRunning,
    };
  },
};
</script>

<style scoped>
/* 工业风格表单样式 */
.industrial-form {
  background: transparent;
}

.simulation-form {
  background: transparent;
}

.form-section {
  margin-bottom: 25px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(116, 185, 255, 0.2);
  backdrop-filter: blur(10px);
}

.section-title {
  color: #74b9ff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  text-shadow: 0 0 8px rgba(116, 185, 255, 0.4);
  border-bottom: 2px solid rgba(116, 185, 255, 0.3);
  padding-bottom: 10px;
}

.section-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.form-item {
  margin-bottom: 18px;
}

.form-item :deep(.el-form-item__label) {
  color: #dfe6e9 !important;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
}

/* 工业风格输入框 */
.industrial-input {
  width: 100%;
}

.industrial-input :deep(.el-input-number) {
  width: 100%;
}

.industrial-input :deep(.el-input__wrapper) {
  background: rgba(45, 52, 54, 0.8) !important;
  border: 1px solid rgba(116, 185, 255, 0.3) !important;
  border-radius: 6px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.industrial-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(116, 185, 255, 0.6) !important;
  box-shadow: 0 0 8px rgba(116, 185, 255, 0.3);
}

.industrial-input :deep(.el-input__wrapper.is-focus) {
  border-color: #74b9ff !important;
  box-shadow: 0 0 12px rgba(116, 185, 255, 0.5);
}

.industrial-input :deep(.el-input__inner) {
  color: #dfe6e9 !important;
  background: transparent !important;
  font-weight: 500;
}

.industrial-input :deep(.el-input-number__increase),
.industrial-input :deep(.el-input-number__decrease) {
  background: rgba(116, 185, 255, 0.1) !important;
  border-color: rgba(116, 185, 255, 0.3) !important;
  color: #74b9ff !important;
}

.industrial-input :deep(.el-input-number__increase:hover),
.industrial-input :deep(.el-input-number__decrease:hover) {
  background: rgba(116, 185, 255, 0.2) !important;
  color: #ffffff !important;
}

/* 工业风格选择器 */
.industrial-select {
  width: 100%;
}

.industrial-select :deep(.el-select__wrapper) {
  background: rgba(45, 52, 54, 0.8) !important;
  border: 1px solid rgba(116, 185, 255, 0.3) !important;
  border-radius: 6px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.industrial-select :deep(.el-select__wrapper:hover) {
  border-color: rgba(116, 185, 255, 0.6) !important;
}

.industrial-select :deep(.el-select__selected-item) {
  color: #dfe6e9 !important;
}

/* 工业风格开关 */
.switch-label {
  color: #dfe6e9;
  font-weight: 500;
  font-size: 14px;
}

.industrial-switch :deep(.el-switch__core) {
  border: 2px solid rgba(116, 185, 255, 0.3);
  background: rgba(45, 52, 54, 0.8);
}

.industrial-switch :deep(.el-switch__action) {
  background: #74b9ff;
  box-shadow: 0 0 8px rgba(116, 185, 255, 0.5);
}

/* 圆角参数动画 */
.rounded-params {
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 200px;
    transform: translateY(0);
  }
}

/* 过辊网格布局 */
.roller-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* 顶点配置网格 */
.vertices-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.vertex-group {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid rgba(116, 185, 255, 0.1);
}

.vertex-title {
  color: #00b894;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-align: center;
  text-shadow: 0 0 6px rgba(0, 184, 148, 0.4);
}

.vertex-coords {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.coord-item {
  margin-bottom: 0 !important;
}

.industrial-input.small :deep(.el-input-number) {
  font-size: 13px;
}

/* 运行按钮 */
.run-button-container {
  margin: 30px 0 0 0;
  text-align: center;
}

.run-button {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 15px 40px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4) !important;
  transition: all 0.3s ease !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.run-button:hover {
  background: linear-gradient(135deg, #00a085 0%, #00b894 100%) !important;
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.6) !important;
  transform: translateY(-2px) !important;
}

.run-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 10px rgba(0, 184, 148, 0.4) !important;
}

.run-button.is-loading {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%) !important;
  box-shadow: 0 4px 15px rgba(99, 110, 114, 0.4) !important;
}

.button-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .roller-grid,
  .vertices-grid {
    grid-template-columns: 1fr;
  }

  .form-section {
    padding: 15px;
  }

  .run-button {
    padding: 12px 30px !important;
    font-size: 14px !important;
  }
}
</style>
