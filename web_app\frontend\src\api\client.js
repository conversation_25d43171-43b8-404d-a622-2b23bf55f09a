import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://127.0.0.1:8888/api/v1', // The base URL of the FastAPI backend
  headers: {
    'Content-Type': 'application/json',
  },
});

export default {
  startSimulation(params) {
    return apiClient.post('/simulations/', params);
  },
  getSimulationStatus(simulationId) {
    return apiClient.get(`/simulations/${simulationId}/status`);
  },
  getSimulationResults(simulationId) {
    return apiClient.get(`/simulations/${simulationId}/results`);
  },
  getSimulations() {
    return apiClient.get('/simulations/');
  },
  exportSimulationResults(simulationId) {
    return apiClient.get(`/simulations/${simulationId}/export`, {
      responseType: 'blob', // Important for file downloads
    });
  },
  deleteSimulation(simulationId) {
    return apiClient.delete(`/simulations/${simulationId}`);
  },
};
