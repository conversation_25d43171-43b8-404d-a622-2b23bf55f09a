[project]
name = "simulator"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.116.1",
    "imageio>=2.37.0",
    "imageio-ffmpeg>=0.6.0",
    "matplotlib>=3.10.3",
    "numpy>=2.3.1",
    "openpyxl>=3.1.5",
    "pandas>=2.3.1",
    "pillow>=11.2.1",
    "psycopg2>=2.9.10",
    "sqlalchemy>=2.0.42",
    "uvicorn>=0.23.2",
]

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
]
