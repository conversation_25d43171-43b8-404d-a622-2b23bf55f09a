#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发环境启动脚本
同时启动前端和后端服务
"""

import subprocess
import sys
import os
import time
import signal
import threading
from pathlib import Path

class DevServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        backend_dir = Path(__file__).parent / "backend"
        
        try:
            # 使用uvicorn启动FastAPI应用
            self.backend_process = subprocess.Popen(
                ["uv", "run", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 在后台线程中读取输出
            def read_backend_output():
                for line in iter(self.backend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[后端] {line.rstrip()}")
                    else:
                        break
            
            backend_thread = threading.Thread(target=read_backend_output, daemon=True)
            backend_thread.start()
            
            print("✅ 后端服务启动成功 (http://localhost:8000)")
            return True
            
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🚀 启动前端服务...")
        frontend_dir = Path(__file__).parent / "frontend"
        
        try:
            # 检查是否已安装依赖
            if not (frontend_dir / "node_modules").exists():
                print("📦 安装前端依赖...")
                install_process = subprocess.run(
                    ["npm", "install"],
                    cwd=frontend_dir,
                    capture_output=True,
                    text=True
                )
                if install_process.returncode != 0:
                    print(f"❌ 依赖安装失败: {install_process.stderr}")
                    return False
                print("✅ 前端依赖安装完成")
            
            # 启动开发服务器
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 在后台线程中读取输出
            def read_frontend_output():
                for line in iter(self.frontend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[前端] {line.rstrip()}")
                    else:
                        break
            
            frontend_thread = threading.Thread(target=read_frontend_output, daemon=True)
            frontend_thread.start()
            
            print("✅ 前端服务启动成功 (http://localhost:5173)")
            return True
            
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return False
    
    def stop_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止服务...")
        self.running = False
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ 后端服务已停止")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("⚠️ 强制停止后端服务")
            except Exception as e:
                print(f"❌ 停止后端服务时出错: {e}")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ 前端服务已停止")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("⚠️ 强制停止前端服务")
            except Exception as e:
                print(f"❌ 停止前端服务时出错: {e}")
    
    def run(self):
        """运行开发服务器"""
        print("=" * 60)
        print("🎯 六边形卷针仿真系统 - 开发环境")
        print("=" * 60)
        
        # 设置信号处理器
        def signal_handler(signum, frame):
            self.stop_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动后端
            if not self.start_backend():
                return 1
            
            # 等待后端启动
            time.sleep(3)
            
            # 启动前端
            if not self.start_frontend():
                self.stop_services()
                return 1
            
            print("\n" + "=" * 60)
            print("🎉 开发环境启动完成！")
            print("=" * 60)
            print("📱 前端地址: http://localhost:5173")
            print("🔧 后端API: http://localhost:8000")
            print("📚 API文档: http://localhost:8000/docs")
            print("=" * 60)
            print("按 Ctrl+C 停止服务")
            print("=" * 60)
            
            # 保持运行
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ 后端服务意外停止")
                    break
                    
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("❌ 前端服务意外停止")
                    break
            
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_services()
        
        return 0

def main():
    """主函数"""
    # 检查依赖
    try:
        subprocess.run(["uv", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到 uv 包管理器，请先安装 uv")
        print("安装命令: pip install uv")
        return 1
    
    try:
        subprocess.run(["npm", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到 npm，请先安装 Node.js")
        return 1
    
    # 启动开发服务器
    server = DevServer()
    return server.run()

if __name__ == "__main__":
    sys.exit(main())
