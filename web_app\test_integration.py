#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前后端集成测试脚本
验证仿真参数传递和结果返回
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from backend.app.simulation import HexagonSimulation
import json

def test_simulation_with_default_params():
    """测试使用默认参数的仿真"""
    print("=" * 60)
    print("测试默认参数仿真")
    print("=" * 60)
    
    # 使用与前端一致的默认参数
    params = {
        'pin_shape': 'hexagon',
        'rotation_center_x_offset': 2.0,
        'film_thickness': 0.1,
        'total_rotation': 720,  # 减少到2圈用于快速测试
        'step_angle': 2.0,      # 增大步长用于快速测试
        'use_rounded_corners': False,
        'sharp_radius': 0.8,
        'blunt_radius': 12.0,
        'roller_A_x': 0.5,
        'roller_A_y': 80.0,
        'roller_B_x': -30.0,
        'roller_B_y': 80.0,
        'roller_radius': 2.0,
        'v1_x': -30.0, 'v1_y': 0.0,
        'v2_x': -20.0, 'v2_y': -4.0,
        'v3_x': 20.0,  'v3_y': -4.0,
        'v4_x': 30.0,  'v4_y': 0.0,
        'v5_x': 20.0,  'v5_y': 4.0,
        'v6_x': -20.0, 'v6_y': 4.0,
    }
    
    print("仿真参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    try:
        # 创建仿真对象
        sim = HexagonSimulation(**params)
        print(f"\n✅ 仿真对象创建成功")
        print(f"数据点数量: {len(sim.theta_deg)}")
        
        # 运行仿真
        print("\n🚀 开始运行仿真...")
        sim.run_simulation()
        print("✅ 仿真运行完成")
        
        # 获取结果
        results = sim.get_simulation_data()
        print(f"\n📊 仿真结果:")
        print(f"  最终角度: {results['theta_deg'][-1]:.1f}°")
        print(f"  最终包覆长度: {results['S'][-1]:.2f} mm")
        print(f"  总薄膜长度: {results['S_total'][-1]:.2f} mm")
        print(f"  最终层数: {results['layer_numbers'][-1]}")
        print(f"  最大厚度: {results['accumulated_thickness'][-1]:.3f} mm")
        
        # 验证关键数据结构
        required_fields = ['theta_deg', 'theta', 'L', 'S', 'S_total', 
                          'contact_points', 'contact_type', 'roller_contact_points',
                          'upper_point_trajectory', 'lower_point_trajectory',
                          'layer_numbers', 'accumulated_thickness', 'original_vertices']
        
        print(f"\n🔍 数据结构验证:")
        for field in required_fields:
            if field in results:
                if hasattr(results[field], '__len__'):
                    print(f"  ✅ {field}: {len(results[field])} 个数据点")
                else:
                    print(f"  ✅ {field}: {type(results[field])}")
            else:
                print(f"  ❌ {field}: 缺失")
        
        return True, results
        
    except Exception as e:
        print(f"❌ 仿真失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_rounded_corners_simulation():
    """测试圆角仿真"""
    print("\n" + "=" * 60)
    print("测试圆角仿真")
    print("=" * 60)
    
    params = {
        'rotation_center_x_offset': 2.0,
        'film_thickness': 0.1,
        'total_rotation': 360,  # 1圈测试
        'step_angle': 5.0,      # 大步长快速测试
        'use_rounded_corners': True,
        'sharp_radius': 0.8,
        'blunt_radius': 12.0,
    }
    
    try:
        sim = HexagonSimulation(**params)
        print(f"✅ 圆角仿真对象创建成功")
        
        sim.run_simulation()
        print("✅ 圆角仿真运行完成")
        
        results = sim.get_simulation_data()
        print(f"📊 圆角仿真结果:")
        print(f"  最终包覆长度: {results['S'][-1]:.2f} mm")
        print(f"  总薄膜长度: {results['S_total'][-1]:.2f} mm")
        
        return True
        
    except Exception as e:
        print(f"❌ 圆角仿真失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 前后端集成测试")
    print("测试仿真核心功能和参数传递")
    
    # 测试1: 默认参数仿真
    success1, results = test_simulation_with_default_params()
    
    # 测试2: 圆角仿真
    success2 = test_rounded_corners_simulation()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"默认参数仿真: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"圆角仿真: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！前后端集成正常。")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
